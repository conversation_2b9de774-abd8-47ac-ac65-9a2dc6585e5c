package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.MsDIProperties;
import com.safaricom.dxl.data.dto.*;
import com.safaricom.dxl.data.entities.OrgEntity;
import com.safaricom.dxl.data.entities.RoleEntity;
import com.safaricom.dxl.data.entities.UsersEntity;
import com.safaricom.dxl.data.enums.*;
import com.safaricom.dxl.data.pojos.*;
import com.safaricom.dxl.data.repositories.*;
import com.safaricom.dxl.exception.DuplicateRecordException;
import com.safaricom.dxl.exception.ForbiddenException;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.model.WsResponseDetails;
import com.safaricom.dxl.webflux.starter.model.WsTransactionalDetails;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;

import java.util.*;

import static com.safaricom.dxl.data.enums.RoleCategory.SP;
import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.MsStarterVariables.ERR_FORBIDDEN;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class Shared {
    private final MsConfigProperties msConfigProperties;
    private final WsMappingService wsMappingService;
    private final KmsEncryption encryption;
    private final WsResponseMapper responseMapper;
    private final RoleRepository rolesRepository;
    private final ReactiveValueOperations<String, String> reactiveValueOperations;
    private final MyWebClient client;
    private final UsersRepository usersRepository;
    private final OrgRepository orgRepository;
    private final Iprs iprs;
    private final SSOToken ssoToken;
    private final UserRepositoryPort userRepositoryPort;
    private final MsDIProperties diProperties;
    private final PermissionRepository permissionRepository;
    private final KafkaStreamingService kafkaStreamingService;

    public WsResponseDetails details(Map<String, String> headers, String customerMessage, String techMessage, String errCode, String processParam) {
        WsResponseDetails responseDetails = wsMappingService.getErrorMapper(errCode, headers.get(X_CONVERSATION_ID), processParam);
        responseDetails.setCode(errCode);
        responseDetails.setCustomerMessage(customerMessage);
        responseDetails.setTechnicalMessage(techMessage);
        return responseDetails;
    }

    public WsResponseDetails details(Map<String, String> headers, String customerMessage, String errCode, String processParam) {
        WsResponseDetails responseDetails = wsMappingService.getErrorMapper(errCode, headers.get(X_CONVERSATION_ID), processParam);
        responseDetails.setCode(errCode);
        responseDetails.setStatus(Integer.parseInt(errCode));
        responseDetails.setCustomerMessage(customerMessage);
        responseDetails.setTechnicalMessage(customerMessage);
        return responseDetails;
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, String customerMessage, String techMessage, String code, String transactionType) {
        WsResponseDetails responseDetails = details(headers, customerMessage, techMessage, code, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, String message, String code, String transactionType) {
        WsResponseDetails responseDetails = details(headers, message, message, code, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, String message, String transactionType) {
        WsResponseDetails responseDetails = details(headers, message, ERR_FORBIDDEN, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    public Mono<WsResponse> customResponse(Map<String, String> headers, PartnerInfo partnerInfo, String message, String code, String transactionType) {
        streamToKafka(headers, partnerInfo, code, message);
        WsResponseDetails responseDetails = details(headers, message, message, code, transactionType);
        return responseMapper.setApiResponse(responseDetails, NULL, transactionType, new WsTransactionalDetails(), NULL, NULL, ES, FALSE, headers);
    }

    private boolean validateLocalMobile(String mobile){
        return String.valueOf(mobile.charAt(0)).equals("+")
                ? regexValidation(mobile, "^\\+(?:[0-9]?){6,14}[0-9]$")
                : regexValidation(mobile, null);

    }

    public Mono<ErrorMapping> validateOrgDetails(OrgDto dto) {
        if ((dto.getOrgName() != null || !dto.getOrgName().isEmpty())  && dto.getOrgName().matches("^[\\w\\s\\.\\&\\-\\'\\()]{1,100}$")) {
            if (EnumUtils.isValidEnumIgnoreCase(OutletTypes.class, dto.getOutletType())) {
                if (EnumUtils.isValidEnumIgnoreCase(OrganizationTypes.class, dto.getOrgType())){
                    return Mono.just(validateCode(dto.getCode()));
                } else
                    return Mono.just(new ErrorMapping("Kindly enter a valid organisation type."));
            } else
                return Mono.just(new ErrorMapping("Kindly enter a valid outlet type."));
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid organisation name."));
    }

    public Mono<ErrorMapping> validateOrgBranchDetails(OrgBranchDto dto) {
        if (dto.getOrgId() != null) {
            if ((dto.getBranchName() != null || !dto.getBranchName().isEmpty()) && dto.getBranchName().matches(NAME_VALIDATION_REGEX))
                    return Mono.just(validateCode(dto.getBranchCode()));
            else
                return Mono.just(new ErrorMapping("Kindly enter a valid branch name."));
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid organisation id."));
    }

    public Mono<ErrorMapping> validateClusterDetails(ClusterDto dto) {
        if ((dto.getCluster() != null || !dto.getCluster().isEmpty()) && dto.getCluster().matches(NAME_VALIDATION_REGEX)) {
            if ((dto.getRegion() != null || !dto.getRegion().isEmpty()) && dto.getRegion().matches(NAME_VALIDATION_REGEX)) {
                if ((dto.getTerritory() != null || !dto.getTerritory().isEmpty()) && dto.getTerritory().matches(NAME_VALIDATION_REGEX)) {
                    return Mono.just(new ErrorMapping());
                } else
                    return Mono.just(new ErrorMapping("Kindly enter a valid territory name."));
            } else
                return Mono.just(new ErrorMapping("Kindly enter a valid region name."));
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid cluster name."));
    }

    public Mono<ErrorMapping> validateRoleCreation(RoleDto dto) {
            if (EnumUtils.isValidEnumIgnoreCase(RoleCategory.class, dto.getCategory())) {
                if (!dto.getPermissionIds().isEmpty()) {
                    return validateRole(dto.getRoleName());
                } else
                    return Mono.just(new ErrorMapping("Role must have at least one permission. Please assign permissions to progress."));
            } else
                return Mono.just(new ErrorMapping("Kindly enter a valid role category."));
    }

    public Mono<ErrorMapping> validateRole(String role) {
        List<String> roles = new ArrayList<>();
        roles.addAll(List.of(msConfigProperties.getRolesUnderSuperUser().split(",")));

        if (regexValidation(role, "^[\\w\\s]{1,100}$") && roles.contains(role))
            return Mono.just(new ErrorMapping());
        else
            return Mono.just(new ErrorMapping("Kindly enter a valid role name."));
    }

    public Mono<ErrorMapping> validateEmail(String email) {
        if (regexValidation(email, EMAIL_VALIDATION_REGEX)) {
            return Mono.just(new ErrorMapping());
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid email."));
    }

    public Mono<ErrorMapping> validateUserEmail(Map<String, String> headers) {
        return validateEmail(headers.get("email")).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null){
                return validateEmail(headers.get("logged-in-user-email")).flatMap(Mono::just);
            }
            return Mono.just(errorMapping);
        });
    }

    public ErrorMapping validateCode(String code) {
        if (code != null && code.length() >= 6 && code.length() <= 10 && regexValidation(code, "^[A-Za-z0-9_-]+$"))
            return new ErrorMapping(null);
        return new ErrorMapping(msConfigProperties.getPartnerCodeError());
    }

    public Mono<ErrorMapping> validatePermission(PermissionDto dto) {
        if ((dto.getPermission() == null || dto.getPermission().isEmpty()) && !dto.getPermission().matches("^[\\w\\s\\.&-']{1,100}$")) {
            return Mono.just(new ErrorMapping("Invalid permission"));
        }
        return Mono.just(new ErrorMapping());
    }

    public Mono<ErrorMapping> validateRoleAndEmail(Map<String, String> headers){
        return validateEmail(headers.get(EMAIL)).flatMap(emailError -> {
            if (emailError.getErrorMessage() == null){
                return Mono.just(new ErrorMapping());
            } else
                return Mono.just(new ErrorMapping(emailError.getErrorMessage()));
        });
    }

    public Mono<ErrorMapping> validateRoleCodeDto(StatusAndRoleAndCodeDto dto) {
        return validateRole(dto.getRole()).flatMap(roleError -> {
                    if (roleError.getErrorMessage() == null){
                        ErrorMapping codeError = validateCode(dto.getCode());
                        if (codeError.getErrorMessage() == null){
                            if (EnumUtils.isValidEnumIgnoreCase(AccessStatus.class, dto.getStatus()))
                                return pagingValidation(dto.getPageSize());
                            else
                                return Mono.just(new ErrorMapping("Your account is not active. Please get in touch with support"));
                        } else
                            return Mono.just(codeError);
                    } else
                        return Mono.just(roleError);
                 });
    }

    public Mono<ErrorMapping> validateUser(UserDto dto) {
        if ((dto.getEmail() != null || !dto.getEmail().isEmpty()) && regexValidation(dto.getEmail(), EMAIL_VALIDATION_REGEX)) {
            return validatePhone(dto).flatMap(errorMapping -> {
                if (errorMapping.getErrorMessage() == null){
                    if (EnumUtils.isValidEnumIgnoreCase(RegistrationDocumentType.class, dto.getIdType())) {
                        if ((dto.getRegistrationDocId() != null || !dto.getRegistrationDocId().isEmpty()) && regexValidation(dto.getRegistrationDocId(), "^[a-zA-Z0-9]{6,15}$")) {
                            return validateNames(dto);
                        } else
                            return Mono.just(new ErrorMapping("Please enter a valid registration document number."));
                    } else
                        return Mono.just(new ErrorMapping("Kindly enter a valid registration document type."));
                }
                return Mono.just(errorMapping);
            });
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid email. Format: <EMAIL>"));
    }


    public Mono<ErrorMapping> validatePhone(UserDto dto) {
        if ((dto.getPhone() != null || !dto.getPhone().isEmpty()) && validateLocalMobile(dto.getPhone())) {
            return Mono.just(new ErrorMapping());
        }
        return Mono.just(new ErrorMapping("Kindly enter a valid phone number before attaching any documents. Sample format:0722000000 or 722000000."));
    }

    private Mono<ErrorMapping> validateNames(UserDto dto) {
        if ((dto.getFirstName() != null || !dto.getFirstName().isEmpty())  && dto.getFirstName().matches("^[\\w\\s\\-']{1,100}$")) {
            if ((dto.getLastName() != null || !dto.getLastName().isEmpty())  && dto.getLastName().matches("^[\\w\\s\\-']{1,100}$")) {
                if (ROLES_WITH_ORGANIZATION.contains(dto.getRole()) && dto.getCode() == null)
                    return Mono.just(new ErrorMapping("The role you have selected must be associated with an organisation."));
                return validateRole(dto.getRole());
            } else
                return Mono.just(new ErrorMapping("Kindly enter a valid last name as per registration document."));
        } else
            return Mono.just(new ErrorMapping("Kindly enter a valid first name as per registration document."));
    }

    public Mono<ErrorMapping> validateRegistration(UserDto dto) {
        ErrorMapping codeError = validateCode(dto.getCode());
        if (codeError.getErrorMessage() == null){
            if (regexValidation(dto.getRegistrationDocId(), "^[a-zA-Z0-9]{6,15}$")) {
                if ((dto.getPhone() != null || !dto.getPhone().isEmpty()) && validateLocalMobile(dto.getPhone())) {
                    if (regexValidation(dto.getEmail(), EMAIL_VALIDATION_REGEX)) {
                        return Mono.just(new ErrorMapping());
                    }
                    return Mono.just(new ErrorMapping("Kindly enter a valid email. Format: <EMAIL>"));
                }
                return Mono.just(new ErrorMapping("Kindly enter a valid phone number before attaching any documents. Sample format:0722000000 or 722000000."));
            } else
                return Mono.just(new ErrorMapping("Please enter a valid registration document number."));
        }
        return Mono.just(codeError);
    }

    public Mono<ErrorMapping> validateCodeString(String code) {
        if (code == null)
            return Mono.just(new ErrorMapping("Code is empty."));
        else
            return Mono.just(new ErrorMapping());
    }

    public Mono<ErrorMapping> validateSafEmail(String email, RoleCategory role) {
        String[] emailSplit = email.split("@");
        if (role.equals(SP) && !emailSplit[1].equalsIgnoreCase("safaricom.co.ke")){
            return Mono.just(new ErrorMapping("Oops, Safaricom Operator role should have a safaricom email."));
        }else
            return Mono.just(new ErrorMapping());
    }

    public Mono<ErrorMapping> pagingValidation(int pageSize){
        if (pageSize > 200){
            return Mono.just(new ErrorMapping("Page size exceeded"));
        }
        return Mono.just(new ErrorMapping());
    }

    public void decryptUserDetails(UsersEntity usersEntity) {
        usersEntity.setPhone(encryption.decrypt(usersEntity.getPhone()));
        usersEntity.setLastName(encryption.decrypt(usersEntity.getLastName()));
    }

    public UsersEntity getDecryptUserDetails(UsersEntity usersEntity) {
//        usersEntity.setPhone(encryption.decrypt(usersEntity.getPhone()));
        usersEntity.setLastName(encryption.decrypt(usersEntity.getLastName()));
        return usersEntity;
    }

    public UsersEntity encryptUser(UsersEntity user) {
        user.setLastName(encryption.encrypt(user.getLastName()));
        user.setPhone(encryption.encrypt(user.getPhone()));
        user.setRegistrationDocId(encryption.encrypt(user.getRegistrationDocId()));
        return user;
    }

    public UsersEntity encryptUserPhone(UsersEntity user) {
        user.setPhone(encryption.encrypt(user.getPhone()));
        return user;
    }

    public String decryptDocId(String id) {
        return encryption.decrypt(id);
    }

    public Mono<PaginationList> getUserListPaginated(Tuple2<List<UsersEntity>, Long> tuple) {
        List<UsersEntity> usersEntities = tuple.getT1();
        Set<String> roleNames = new HashSet<>();
        usersEntities.forEach(usersEntity -> roleNames.add(usersEntity.getRole()));
        return rolesRepository.findByRoleNameIn(roleNames).collectList()
                .map(roleEntities -> {
                    List<UserResponseA> userResponsAS = new ArrayList<>();
                    roleEntities.forEach(roleEntity -> usersEntities.forEach(usersEntity -> {
                        if (usersEntity.getRole().equalsIgnoreCase(roleEntity.getRoleName())) {
                            decryptUserDetails(usersEntity);
                            userResponsAS.add(usersEntity.toUserResponse(roleEntity.toUserRoleResponse()));
                        }
                    }));
                    return userResponsAS;
                })
                .flatMap(userResponses -> Mono.just(new PaginationList(tuple.getT2(), userResponses)))
                .switchIfEmpty(Mono.defer(() -> Mono.just(new PaginationList(tuple.getT2(), tuple.getT1()))));
    }

    public List<String> getRoleNamesUnder(String roleName){
        if (roleName.equalsIgnoreCase("Super User"))
            return List.of(msConfigProperties.getRolesUnderSuperUser().split(","));
        else if (roleName.equalsIgnoreCase("rtc partner support"))
            return List.of(msConfigProperties.getRolesUnderRtcPartnerSupport().split(","));
        else if(roleName.equalsIgnoreCase("director"))
            return List.of(msConfigProperties.getRolesUnderDirector().split(","));
        else if(roleName.equalsIgnoreCase("administrator"))
            return List.of(msConfigProperties.getRolesUnderAdministrator().split(","));
        else
            return List.of(msConfigProperties.getRolesUnderSuperUser().split(","));
    }

    public List<String> getRoleHierarchy(String roleName){
        List<String> list = new ArrayList<>();
        list.addAll(List.of(msConfigProperties.getRolesUnderSuperUser().split(",")));
        if (roleName.equalsIgnoreCase("Super User")) {
            list.remove("Super User");
            return list;
        } else if (roleName.equalsIgnoreCase("rtc partner support")){
            list.remove("Super User");
            list.remove("RTC Partner Support");
            return list;
        }
        else if(roleName.equalsIgnoreCase("Director")) {
            list.remove("Super User");
            list.remove("RTC Partner Support");
            list.remove("Director");
            return list;
        } else
            return List.of("Unknown");
    }

    public IdentityRegRequest getIdentityRequest(UsersEntity user, boolean encrypted) {
        IdentityRegRequest request = new IdentityRegRequest();
        request.setFirstName(user.getFirstName());
        if (encrypted) {
            request.setLastName(encryption.decrypt(user.getLastName()));
            request.setMsisdn(encryption.decrypt(user.getPhone()));
        } else {
            request.setLastName(user.getLastName());
            request.setMsisdn(user.getPhone());
        }
        request.setEmail(user.getEmail());
        request.setService(diProperties.getService());
        request.setClient(diProperties.getClientId());
        request.setRealm(diProperties.getRealmId());
        request.setContinuePath(diProperties.getContinuePath());
        request.setEmailConfirmation(diProperties.getEmailConfirmation());
        return request;
    }

    public void sendRegistrationRequest(Map<String, String> headers, UsersEntity usersEntity, boolean encrypted) {
        IdentityRegRequest identityRegRequest = getIdentityRequest(usersEntity, encrypted);
        client.identityRegSts(headers).post()
                .header(X_MSISDN, formatMsisdn(identityRegRequest.getMsisdn()))
                .body(Mono.just(identityRegRequest), IdentityRegRequest.class)
                .retrieve()
                .bodyToMono(DxlApiResponse.class)
                .flatMap(Mono::just)
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), TRANS_IDENTITY_REGISTRATION_FAILED, "", err.getMessage()))
                .onErrorResume(throwable -> Mono.empty())
                .subscribe();
    }

    /**
     *
     * @param headers
     * @param user
     * @param transactionType
     * @return
     */
    public Mono<WsResponse> returnUserDetails(Map<String, String> headers, UsersEntity user, String transactionType ) {
        return rolesRepository.findByRoleName(user.getRole())
                .flatMap(roleEntity -> {
                    decryptUserDetails(user);
                    if (user.getCodes() == null || user.getCodes().isEmpty())
                        return responseMapper.setApiResponse(ERR_SUCCESS, user.toUserResponse(roleEntity.toUserRoleResponse()), transactionType, FALSE, headers);
                    else
                        return orgRepository.findByCodeIn(user.getCodes()).collectList()
                                .flatMap(orgEntities -> {
                                    List<OrgDetailResponse> orgDetailResponses = new ArrayList<>();
                                    orgEntities.forEach(orgEntity -> orgDetailResponses.add(orgEntity.toOrgDetailResponse()));
                                    return responseMapper.setApiResponse(ERR_SUCCESS, user.toUserResponse(roleEntity.toUserRoleResponse(), orgDetailResponses), transactionType, FALSE, headers);
                                })
                                .switchIfEmpty(Mono.defer(() -> responseMapper.setApiResponse(ERR_SUCCESS, user.toUserResponse(roleEntity.toUserRoleResponse()), transactionType, FALSE, headers)));

                });
    }

    public Mono<WsResponse> processDeLinkCodeFromUser(Map<String, String> headers, UsersEntity usersEntity) {
        removeCodeIfExists(headers, usersEntity);
        return usersRepository.save(usersEntity)
                .flatMap(user -> returnUserDetails(headers, user, TRANS_USER_UPDATE));
    }

    public void removeCodeIfExists(Map<String, String> headers, UsersEntity usersEntity) {
        if (usersEntity.getCodes() != null) {
            usersEntity.getCodes().forEach(code -> {
                if (code.equalsIgnoreCase(headers.get("code"))) {
                    usersEntity.getCodes().remove(headers.get("code"));
                }
            });
        }
    }

    public void setOrgHoCode(OrgDto dto, OrgEntity orgEntity) {
        if (dto.getHoStoreNumber() != null && StringUtils.isNumeric(dto.getHoStoreNumber()))
            orgEntity.setHoStoreNumber(dto.getHoStoreNumber());
    }

    public Mono<ErrorMapping> validateSession(Map<String, String> headers) {
        if (headers.get("session_id") == null || headers.get("session_id").isEmpty())
            return Mono.just(new ErrorMapping("No SID."));
        return reactiveValueOperations.get(headers.get("session_id"))
                .flatMap(s -> Mono.just(new ErrorMapping()))
                .switchIfEmpty(Mono.defer(() -> Mono.just(new ErrorMapping("Session invalid"))));
    }

    public Mono<ErrorMapping> validateRoleDto(RoleUpdateDto roleDto) {
        if (roleDto.getPermissionIds().isEmpty()) {
            return Mono.just(new ErrorMapping("You must have atleast one permission."));
        }
        return Mono.just(new ErrorMapping());
    }

    public Mono<WsResponse> processUpdateOtpRequest(Map<String, String> headers, UserDto dto) {
        return usersRepository.findById(dto.getId())
                .flatMap(usersEntity -> {
                    if (usersEntity.isEnabled()) {
                        dto.setRegistrationDocId(decryptDocId(usersEntity.getRegistrationDocId()));
                        return iprs.validateNoIdType(headers, dto, false)
                                .flatMap(iprsErrorMapping -> {
                                    if (ALLOWED_SSO_REGISTRATION.contains(iprsErrorMapping.getStatus())) {
                                        usersEntity.setPhone(formatMsisdn(dto.getPhone()));
                                        usersEntity.setUpdated(localDateTime());
                                        usersEntity.setUpdatedBy(headers.get(X_IDENTITY));
                                        return ssoToken.updateOtp(headers, getDecryptUserDetails(usersEntity))
                                                .flatMap(errorMapping -> {
                                                    if (errorMapping.getErrorMessage() == null)
                                                        return userRepositoryPort.updateMsisdn(encryptUserPhone(usersEntity))
                                                                .flatMap(s -> responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_USER_UPDATE, FALSE, headers));
                                                    return customResponse(headers, "Request failed, Please try again later", errorMapping.getErrorMessage(), ERR_BAD_REQUEST, TRANS_USER_FETCH);
                                                });
                                    }
                                    return customResponse(headers, "Phone number is not registered to the users document ID. Ensure to use Phone number registered to the user.", ERR_NOT_FOUND, TRANS_USER_FETCH);
                                });
                    }
                    return customResponse(headers, "You cannot change OTP for an inactive user. Onboard the user with correct details.", ERR_VALIDATION, TRANS_USER_UPDATE);
                })
                .switchIfEmpty(Mono.defer(() -> customResponse(headers, "User not found", ERR_NOT_FOUND, TRANS_USER_FETCH)));
    }

    public Mono<WsResponse> validatePermission(Map<String, String> headers, RoleUpdateDto roleDto, RoleEntity role) {
        return permissionRepository.findAllById(roleDto.getPermissionIds()).collectList()
                .map(permissionEntities -> {
                    Set<String> permissionIds = new HashSet<>();
                    permissionEntities.forEach(permission -> permissionIds.add(permission.getId()));
                    return permissionIds;
                })
                .flatMap(permissionIds -> {
                    if (permissionIds.isEmpty())
                        return customResponse(headers, "Invalid Permissions", ERR_VALIDATION, TRANS_ROLE_FETCH);
                    if (roleDto.isAdd())
                        role.getPermissionIds().addAll(permissionIds);
                    else
                        role.getPermissionIds().removeAll(permissionIds);
                    return rolesRepository.save(role)
                            .flatMap(role1 -> responseMapper.setApiResponse(ERR_SUCCESS, role1.toRoleResponse(), TRANS_ROLE_FETCH, FALSE, headers));
                });
    }

    public void streamToKafka(Map<String, String> headers, PartnerInfo partnerInfo, String errorCode, String errorMessage) {
        if (msConfigProperties.isEnableStreaming()) {
            DataStreaming dataStreaming = new DataStreaming();
            dataStreaming.setSourceSystem(headers.get(X_SOURCE_SYSTEM));
            dataStreaming.setServiceName(SERVICE_NAME);
            dataStreaming.setResource(partnerInfo.getReportName());
            dataStreaming.setDealerCode(partnerInfo.getDealerCode());
            dataStreaming.setDealerName(partnerInfo.getDealerName());
            dataStreaming.setRole(stripString(partnerInfo.getRole()));
            dataStreaming.setTimeStamp(partnerInfo.getStartTime());
            dataStreaming.setTransactionId(headers.get(X_CONVERSATION_ID));
            dataStreaming.setResponseCode(errorCode == null ? ERR_SUCCESS : errorCode);
            dataStreaming.setResponseMessage(errorMessage == null ? "success" : errorMessage);
            dataStreaming.setTransactional(partnerInfo.getTransactionValue() != null && !partnerInfo.getTransactionValue().isNaN());
            dataStreaming.setTransactionValue(partnerInfo.getTransactionValue() != null && !partnerInfo.getTransactionValue().isNaN() ? partnerInfo.getTransactionValue() : 0L);
            dataStreaming.setResponseTime(System.currentTimeMillis() - partnerInfo.getStartTime());
            dataStreaming.setExtraInfo(partnerInfo.getAdditionalData());
            dataStreaming.setUsername(stripString(partnerInfo.getEmail()));
            kafkaStreamingService.streamToKafka(dataStreaming, partnerInfo.getStartTime());
        }
    }

    public static boolean returnChainedException(Throwable throwable) {
        return throwable instanceof ForbiddenException || throwable instanceof InternalServerErrorException || throwable instanceof NotFoundException || throwable instanceof DuplicateRecordException;
    }
}