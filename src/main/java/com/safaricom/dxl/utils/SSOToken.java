package com.safaricom.dxl.utils;

import com.safaricom.dxl.config.MsDIProperties;
import com.safaricom.dxl.data.entities.UsersEntity;
import com.safaricom.dxl.data.pojos.DxlApiResponse;
import com.safaricom.dxl.data.pojos.ErrorMapping;
import com.safaricom.dxl.data.pojos.TokenErrorMapping;
import com.safaricom.dxl.webflux.starter.service.WsStarterService;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class SSOToken {
    private final ReactiveValueOperations<String, String> reactiveValueOperations;
    private final MyWebClient client;
    private final MsDIProperties diProperties;
    private final WsStarterService wsStarterService;

    public Mono<TokenErrorMapping> validation(Map<String, String> headers){
        if (diProperties.isSsoEnabled()){
            return validateForToken(headers)
                    .subscribeOn(Schedulers.boundedElastic())
                    .flatMap(errorMapping -> {
                        if (errorMapping.getErrorMessage() == null){
                            String uuidOfXToken = generateId(headers.get(X_TOKEN).trim());
                            return reactiveValueOperations.get(uuidOfXToken)
                                    .flatMap(token -> Mono.just(new TokenErrorMapping()))
                                    .switchIfEmpty(Mono.defer(() -> tokenValidationStsLogic(headers, uuidOfXToken)));
                        } else
                            return Mono.just(new TokenErrorMapping(errorMapping.getErrorMessage()));
                    });
        }
        return Mono.just(new TokenErrorMapping());
    }

    public Mono<ErrorMapping> validateForToken(Map<String, String> headers) {
        if (headers.get(X_TOKEN) == null || headers.get(X_TOKEN).isEmpty())
            return Mono.just(new ErrorMapping("SSO Token is missing."));
        else
            return Mono.just(new ErrorMapping());
    }

    private Mono<TokenErrorMapping> tokenValidationStsLogic(Map<String, String> headers, String uuidOfXToken) {
        AtomicReference<String> email = new AtomicReference<>("");
        return client.validateSsoTokenSts().get()
                .header(X_MSISDN, formatMsisdn(headers.get(X_MSISDN)))
                .header(X_TOKEN, headers.get(X_TOKEN))
                .retrieve()
                .bodyToMono(DxlApiResponse.class)
                .flatMap(dxlApiResponse -> {
                    if( dxlApiResponse.getHeader().getResponseCode() == 200) {
                        JSONObject o =  new JSONObject(wsStarterService.serialize(dxlApiResponse.getBody()));
                        email.set(o.getString("emailAddress"));
                        if (email.get() != null || !email.get().isEmpty()){
                            reactiveValueOperations.set(uuidOfXToken, email.get().trim(), diProperties.getTtl()).subscribe();
                        }
                        return Mono.just(new TokenErrorMapping(null, email.get()));
                    } else
                        return Mono.just(new TokenErrorMapping("Session timed out. Please reload the page and login again"));
                })
                .doOnError(throwable -> starterError(headers.get(X_CONVERSATION_ID),"validateSsoTokenSts error","", throwable.getMessage()))
                .onErrorResume(throwable -> Mono.just(new TokenErrorMapping("Request failed. Please try again later.")));
    }

    public void logout(Map<String, String> headers) {
        reactiveValueOperations.delete(generateId(headers.get(X_TOKEN).trim())).subscribe();
        if (headers.get("session_id") != null || !headers.get("session_id").isEmpty())
            reactiveValueOperations.delete(generateId(headers.get("session_id").trim())).subscribe();
    }

    public Mono<ErrorMapping> updateOtp(Map<String, String> headers, UsersEntity usersEntity){
        String body = new JSONObject()
                        .put("firstName", usersEntity.getFirstName())
                        .put("lastName", usersEntity.getLastName())
                        .put("msisdn", usersEntity.getPhone())
                        .put("email", usersEntity.getEmail()).toString();
        return client.updateSsoOtpSts().put()
                        .header(X_MSISDN, usersEntity.getPhone())
                        .header(X_TOKEN, headers.get(X_TOKEN))
                        .header(X_SOURCE_SYSTEM, "dxl-ms-starter")
                        .bodyValue(body)
                        .retrieve()
                        .bodyToMono(DxlApiResponse.class)
                        .flatMap(dxlApiResponse -> {
                            if( dxlApiResponse.getHeader().getResponseCode() == 200)
                                return Mono.just(new ErrorMapping());
                            return Mono.just(new ErrorMapping(dxlApiResponse.getHeader().getResponseMessage()));
                        })
                        .doOnError(throwable -> starterError(headers.get(X_CONVERSATION_ID),"Update otp error","", throwable.getMessage()))
                        .onErrorResume(throwable -> Mono.just(new ErrorMapping(throwable.getMessage())));
    }
}
