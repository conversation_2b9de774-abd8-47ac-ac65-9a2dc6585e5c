package com.safaricom.dxl.mapper;

import com.safaricom.dxl.data.dto.OrganizationTypeDto;
import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.data.entities.OrganizationTypeEntity;
import com.safaricom.dxl.data.entities.OutletTypeEntity;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface LogMapper {

    LogMapper INSTANCE = Mappers.getMapper(LogMapper.class);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void mapOrgType(OrganizationTypeDto source, @MappingTarget OrganizationTypeEntity target);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void mapOutletType(OutletTypeDto source, @MappingTarget OutletTypeEntity target);
}
