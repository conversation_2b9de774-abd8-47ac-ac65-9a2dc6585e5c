package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.ClusterDto;
import com.safaricom.dxl.data.entities.ClustersEntity;
import com.safaricom.dxl.data.pojos.PaginationList;
import com.safaricom.dxl.data.repositories.ClustersRepository;
import com.safaricom.dxl.service.ClusterService;
import com.safaricom.dxl.utils.MsStarterVariables;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.utils.execution.LogExecutionTime;
import com.safaricom.dxl.webflux.starter.annotation.WsProcess;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class ClusterServiceImpl implements ClusterService {

    private final WsResponseMapper responseMapper;
    private final ClustersRepository repository;
    private final Shared shared;
    private final SSOToken ssoToken;

    @WsProcess(TRANS_CLUSTER_CREATION)
    @Override
    @LogExecutionTime
    public Mono<WsResponse> add(Map<String, String> headers, Mono<ClusterDto> payload) {
        return payload
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(dto -> shared.validateClusterDetails(dto)
                .flatMap(e -> {
                    if (e.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(errorMapping -> {
                            if (errorMapping.getErrorMessage() == null) {
                                return repository.findByRegionAndTerritoryAndCluster(dto.getRegion(), dto.getTerritory(), dto.getCluster())
                                        .flatMap(clustersEntity -> {
                                            clustersEntity.setRegion(dto.getRegion());
                                            clustersEntity.setTerritory(dto.getRegion());
                                            clustersEntity.setCluster(dto.getCluster());
                                            clustersEntity.setUpdated(localDateTime());
                                            clustersEntity.setUpdatedBy(headers.get(X_IDENTITY));
                                            return repository.save(clustersEntity)
                                                    .flatMap(clustersEntity1 -> responseMapper.setApiResponse(ERR_SUCCESS, clustersEntity1.toClusterResponse(), TRANS_CLUSTER_CREATION, FALSE, headers));
                                        })
                                        .switchIfEmpty(Mono.defer(() -> {
                                            ClustersEntity clustersEntity = ClustersEntity.of(dto);
                                            clustersEntity.setCreatedBy(headers.get(X_IDENTITY));
                                            return repository.save(clustersEntity)
                                                    .flatMap(clustersEntity1 -> responseMapper.setApiResponse(ERR_SUCCESS, clustersEntity1.toClusterResponse(), TRANS_CLUSTER_CREATION, FALSE, headers));
                                        }));
                            } else
                                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_CLUSTER_CREATION);
                        });
                    } else
                        return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_CLUSTER_CREATION);
        }));
    }

    @WsProcess(TRANS_CLUSTER_FETCH)
    @Override
    @LogExecutionTime
    public Mono<WsResponse> fetchByRegion(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return repository.findByRegion(headers.get("region")).collectList()
                        .map(MsStarterVariables::getClusterResponse)
                        .flatMap(clustersEntities -> responseMapper.setApiResponse(ERR_SUCCESS, clustersEntities, TRANS_CLUSTER_FETCH, FALSE, headers));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_CLUSTER_FETCH);
        });
    }

    @WsProcess(TRANS_CLUSTER_FETCH)
    @Override
    @LogExecutionTime
    public Mono<WsResponse> fetchByTerritory(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return repository.findByTerritory(headers.get("territory")).collectList()
                        .map(MsStarterVariables::getClusterResponse)
                        .flatMap(clustersEntities -> responseMapper.setApiResponse(ERR_SUCCESS, clustersEntities, TRANS_CLUSTER_FETCH, FALSE, headers));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_CLUSTER_FETCH);
        });
    }

    @WsProcess(TRANS_CLUSTER_FETCH)
    @Override
    @LogExecutionTime
    public Mono<WsResponse> fetchByCluster(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return repository.findByCluster(headers.get("cluster")).collectList()
                        .map(MsStarterVariables::getClusterResponse)
                        .flatMap(clustersEntities -> responseMapper.setApiResponse(ERR_SUCCESS, clustersEntities, TRANS_CLUSTER_FETCH, FALSE, headers));
            } else
                 return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_CLUSTER_FETCH);
        });
    }

    @WsProcess(TRANS_CLUSTER_FETCH)
    @Override
    @LogExecutionTime
    public Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize) {
        return shared.pagingValidation(pageSize)
                .flatMap(e -> {
                    if (e.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(errorMapping -> {
                            if (errorMapping.getErrorMessage() == null) {
                                return repository.findBy(getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                        .map(MsStarterVariables::getClusterResponse)
                                        .zipWith(repository.count())
                                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                                        .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_CLUSTER_FETCH, FALSE, headers));
                            } else
                                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_CLUSTER_FETCH);
                        });
                    }
                    return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
                });
    }

}
