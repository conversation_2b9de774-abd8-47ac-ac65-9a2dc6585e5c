package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.RoleDto;
import com.safaricom.dxl.data.dto.RoleUpdateDto;
import com.safaricom.dxl.data.entities.RoleEntity;
import com.safaricom.dxl.data.enums.RoleCategory;
import com.safaricom.dxl.data.pojos.PaginationList;
import com.safaricom.dxl.data.pojos.RoleResponsePojo;
import com.safaricom.dxl.data.repositories.PermissionRepository;
import com.safaricom.dxl.data.repositories.RoleRepository;
import com.safaricom.dxl.service.RoleService;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class RoleServiceImpl implements RoleService {

    private final WsResponseMapper responseMapper;
    private final RoleRepository repository;
    private final PermissionRepository permissionRepository;
    private final Shared shared;
    private final SSOToken ssoToken;

    @Override
    public Mono<WsResponse> add(Map<String, String> headers, Mono<RoleDto> payload) {
        return payload.flatMap(dto -> shared.validateRoleCreation(dto).flatMap(e -> {
            if (e.getErrorMessage() == null){
                return ssoToken.validation(headers).flatMap(error -> {
                    if (error.getErrorMessage() == null) {
                        return repository.findByCategoryAndRoleName(dto.getCategory(), dto.getRoleName())
                                .flatMap(role -> {
                                    role.setRoleName(dto.getRoleName());
                                    role.setCategory(RoleCategory.valueOf(dto.getCategory()));
                                    dto.getPermissionIds().addAll(role.getPermissionIds());
                                    role.setPermissionIds(dto.getPermissionIds());
                                    role.setUpdated(localDateTime());
                                    role.setUpdatedBy(headers.get(X_IDENTITY));
                                    return setPermissionId(headers, dto, role);
                                })
                                .switchIfEmpty(Mono.defer(() -> {
                                    RoleEntity role = RoleEntity.of(dto);
                                    role.setCreatedBy(headers.get(X_IDENTITY));
                                    return  setPermissionId(headers, dto, role);
                                }));
                    } else
                        return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_CREATION);
                });
            } else {
                return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_ROLE_CREATION);
            }
        }));
    }

    private Mono<WsResponse> saveRole(Map<String, String> headers, RoleEntity role) {
        return repository.save(role)
                .flatMap(roleEntity -> responseMapper.setApiResponse(ERR_SUCCESS, roleEntity.toRoleResponse(), TRANS_ROLE_CREATION, FALSE, headers));
    }

    private Mono<WsResponse> setPermissionId(Map<String, String> headers, RoleDto dto, RoleEntity role) {
        Set<String> list = dto.getPermissionIds();
        return permissionRepository.findAllById(list).collectList()
                .flatMap(permissions -> {
                    if (permissions.isEmpty()){
                        return shared.customResponse(headers, "Invalid permission id.", ERR_VALIDATION, TRANS_ROLE_CREATION);
                    } else {
                        Set<String> ids = new HashSet<>();
                        permissions.forEach(permissionEntity -> ids.add(permissionEntity.getId()));
                        role.setPermissionIds(ids);
                        return saveRole(headers, role);
                    }
                });
    }

    @Override
    public Mono<WsResponse> fetchById(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(error -> {
            if (error.getErrorMessage() == null) {
                return repository.findById(Long.valueOf(headers.get("role-id")))
                        .flatMap(rolesEntities -> responseMapper.setApiResponse(ERR_SUCCESS, rolesEntities.toRoleResponse(), TRANS_ROLE_FETCH, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> fetchByRole(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(error -> {
            if (error.getErrorMessage() == null) {
                return repository.findByRoleName(headers.get("role-name"))
                        .flatMap(roles -> responseMapper.setApiResponse(ERR_SUCCESS, roles.toRoleResponse(), TRANS_ROLE_FETCH, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> fetchByCategory(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(error -> {
            if (error.getErrorMessage() == null) {
                return repository.findByCategory(headers.get("category")).collectList()
                        .map(RoleServiceImpl::formatResponse)
                        .flatMap(rolesEntities -> responseMapper.setApiResponse(ERR_SUCCESS, rolesEntities, TRANS_ROLE_FETCH, FALSE, headers));
            } else
                return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_FETCH);
        });
    }

    private static List<RoleResponsePojo> formatResponse(List<RoleEntity> rolesEntities) {
        List<RoleResponsePojo> rolesResponseList = new ArrayList<>();
       rolesEntities.forEach(rolesEntity -> rolesResponseList.add(rolesEntity.toRoleResponse()));
        return rolesResponseList;
    }

    @Override
    public Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize) {
        return shared.pagingValidation(pageSize)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(error -> {
                            if (error.getErrorMessage() == null) {
                                return repository.findBy(getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                        .map(RoleServiceImpl::formatResponse)
                                        .zipWith(repository.count())
                                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                                        .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_ROLE_FETCH, FALSE, headers));
                            } else
                                return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_FETCH);
                        });
                    } else {
                        return shared.customResponse(headers, "Invalid permission id.", ERR_VALIDATION, TRANS_ROLE_FETCH);
                    }
                });
    }
    @Override
    public Mono<WsResponse> addOrRemovePermission(Map<String, String> headers, Mono<RoleUpdateDto> payload) {
        return payload.flatMap(roleDto -> shared.validateRoleDto(roleDto)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(error -> {
                            if (error.getErrorMessage() == null) {
                                return repository.findById(roleDto.getId())
                                        .flatMap(role -> shared.validatePermission(headers, roleDto, role))
                                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Role Not Found", ERR_NOT_FOUND, TRANS_ROLE_FETCH)));
                            }
                            return shared.customResponse(headers, error.getErrorMessage(), TRANS_ROLE_FETCH);
                        });
                    }
                    return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_ROLE_FETCH);
                }));
    }
}
