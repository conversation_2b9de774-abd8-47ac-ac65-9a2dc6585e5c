package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OrganizationTypeDto;
import com.safaricom.dxl.data.entities.OrganizationTypeEntity;
import com.safaricom.dxl.data.entities.OutletTypeEntity;
import com.safaricom.dxl.data.pojos.OrganizationTypeResponse;
import com.safaricom.dxl.data.pojos.OrganizationTypeWithOutletsResponse;
import com.safaricom.dxl.data.pojos.OutletTypeResponse;
import com.safaricom.dxl.data.repositories.OrganizationTypeRepository;
import com.safaricom.dxl.data.repositories.OutletTypeRepository;
import com.safaricom.dxl.exception.DuplicateRecordException;
import com.safaricom.dxl.exception.ForbiddenException;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.mapper.LogMapper;
import com.safaricom.dxl.service.OrganizationTypeService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Shared.returnChainedException;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@RequiredArgsConstructor

public class OrganizationTypeServiceImpl implements OrganizationTypeService {
    private static final Logger log = LoggerFactory.getLogger(OrganizationTypeServiceImpl.class);
    private final OrganizationTypeRepository organizationTypeRepository;
    private final OutletTypeRepository outletTypeRepository;
    private final WsResponseMapper responseMapper;

    @Override
    public Mono<WsResponse> create(final OrganizationTypeDto dto, Map<String, String> headers) {
        return organizationTypeRepository.findByName(dto.getName().trim().toUpperCase())
                .flatMap(existing -> Mono.<WsResponse>error(new DuplicateRecordException("OrganizationType '" + dto.getName() + "' already exists", ERR_CONFLICT)))
                .switchIfEmpty(Mono.defer(() -> saveOrganizationType(dto, headers)))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    private Mono<WsResponse> saveOrganizationType(OrganizationTypeDto dto, Map<String, String> headers) {
        OrganizationTypeEntity entity = OrganizationTypeEntity.builder()
                .name(dto.getName().trim().toUpperCase())
                .description(dto.getDescription().trim())
                .created(LocalDateTime.now())
                .createdBy(headers.get(X_IDENTITY))
                .build();
        return organizationTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_CREATED, organizationTypeResponse, TRANS_POST_ORG_CREATION, FALSE, headers))
                .onErrorResume(throwable -> {
                    if (returnChainedException(throwable)) {
                        return Mono.error(throwable);
                    }
                    return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_WRITE));
                });
    }

    @Override
    public Mono<WsResponse> update(final Long id, final OrganizationTypeDto dto, Map<String, String> headers) {
        return organizationTypeRepository.findById(id)
                .switchIfEmpty(Mono.error(new NotFoundException("OrgType id - ".concat(String.valueOf(id)).concat(" Not Found"), ERR_NOT_FOUND)))
                .flatMap(existing -> updateOrganizationType(dto, existing, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    private Mono<WsResponse> updateOrganizationType(OrganizationTypeDto dto, OrganizationTypeEntity entity, Map<String, String> headers) {
        LogMapper.INSTANCE.mapOrgType(dto, entity);
        entity.setUpdatedBy(headers.get(X_IDENTITY));
        entity.setUpdated(LocalDateTime.now());
        return organizationTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponse, TRANS_PUT_ORG_UPDATE, FALSE, headers))
                .onErrorResume(throwable -> {
                    if (returnChainedException(throwable)) {
                        return Mono.error(throwable);
                    }
                    return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_UPDATE));
                });
    }

    @Override
    public Mono<WsResponse> delete(final Long id, Map<String, String> headers) {
        //You cannot delete organization type if outlet types records that relates to still exists
        return organizationTypeRepository.findById(id)
                .flatMap(organizationTypeEntity -> outletTypeRepository.findByOrganizationTypeId(organizationTypeEntity.getId())
                        .collectList()
                        .flatMap(existing -> {
                            if (!existing.isEmpty()) {
                                return Mono.error(new ForbiddenException("OrganizationType has " + existing.size() + " records", ERR_REC_NOT_EMPTY));
                            }
                            return organizationTypeRepository.deleteById(id)
                                    .doOnSuccess(v -> log.info("Deleted OrganizationType id={}", id))
                                    .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_DEL_ORG_DELETE, FALSE, headers))
                                    .onErrorResume(throwable -> {
                                        if (returnChainedException(throwable)) {
                                            return Mono.error(throwable);
                                        }
                                        return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_DELETE));
                                    });
                        })
                )
                .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType id - ".concat(String.valueOf(id)).concat(" Not Found"), ERR_NOT_FOUND)))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getById(final Long id, Map<String, String> headers) {
        return organizationTypeRepository.findById(id)
                .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType id - ".concat(String.valueOf(id)).concat(" Not Found"), ERR_NOT_FOUND)))
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponse, TRANS_GET_ORG, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getAll(Map<String, String> headers) {
        return organizationTypeRepository.findAll()
                .switchIfEmpty(Mono.error(new NotFoundException("OrganizationTypes is empty", ERR_NOT_FOUND)))
                .map(this::toResponse)
                .collectList()
                .flatMap(organizationTypeResponses -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponses, TRANS_GET_ORG, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getAllWithOutlets(Map<String, String> headers) {
        return organizationTypeRepository.findAll()
                .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType not found", ERR_NOT_FOUND)))
                .flatMap(orgType ->
                        outletTypeRepository.findByOrganizationTypeId(orgType.getId())
                                .map(this::toOutletTypeResponse)
                                .collectList()
                                .map(outlets -> new OrganizationTypeWithOutletsResponse(
                                        orgType.getId(),
                                        orgType.getName(),
                                        orgType.getDescription(),
                                        outlets
                                ))
                                .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ)))
                )
                .collectList()
                .flatMap(organizationTypeWithOutletsResponses -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeWithOutletsResponses, TRANS_BRANCH_CREATION, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    private OrganizationTypeResponse toResponse(OrganizationTypeEntity entity) {
        return OrganizationTypeResponse.builder()
                .id(entity.getId())
                .name(entity.getName())
                .description(entity.getDescription())
                .created(entity.getCreated())
                .createdBy(entity.getCreatedBy())
                .updated(entity.getUpdated())
                .updatedBy(entity.getUpdatedBy())
                .build();
    }

    private OutletTypeResponse toOutletTypeResponse(OutletTypeEntity entity) {
        return OutletTypeResponse.builder()
                .id(entity.getId())
                .dealerName(entity.getDealerName())
                .description(entity.getDescription())
                .dealerCode(entity.getDealerCode())
                .hoCode(entity.getHoCode())
                .organizationTypeId(entity.getOrganizationTypeId())
                .build();
    }
}
