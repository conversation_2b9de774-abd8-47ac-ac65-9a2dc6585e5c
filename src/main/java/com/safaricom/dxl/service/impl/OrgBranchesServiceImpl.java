package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OrgBranchDto;
import com.safaricom.dxl.data.entities.OrgBranchEntity;
import com.safaricom.dxl.data.pojos.PaginationList;
import com.safaricom.dxl.data.repositories.OrgBranchesRepository;
import com.safaricom.dxl.data.repositories.OrgRepository;
import com.safaricom.dxl.service.OrgBranchesService;
import com.safaricom.dxl.utils.MsStarterVariables;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;


/**
 * <AUTHOR>
 */

@Service
@AllArgsConstructor
public class OrgBranchesServiceImpl implements OrgBranchesService {
    private final WsResponseMapper responseMapper;
    private final OrgBranchesRepository orgBranchesRepository;
    private final OrgRepository orgRepository;
    private final Shared shared;
    private final SSOToken ssoToken;

    @Override
    public Mono<WsResponse> add(Map<String, String> headers, Mono<OrgBranchDto> payload) {
        return payload.flatMap(dto -> shared.validateOrgBranchDetails(dto).flatMap(e -> {
            if (e.getErrorMessage() == null){
                return ssoToken.validation(headers).flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return orgBranchesRepository.findByBranchCode(dto.getBranchCode())
                                .flatMap(orgBranch -> {
                                    orgBranch.setBranchName(dto.getBranchName());
                                    orgBranch.setOrgId(dto.getOrgId());
                                    orgBranch.setUpdated(localDateTime());
                                    orgBranch.setUpdatedBy(headers.get(X_IDENTITY));
                                    return setOrgId(headers, dto, orgBranch);
                                })
                                .switchIfEmpty(Mono.defer(() -> {
                                    OrgBranchEntity orgBranch = OrgBranchEntity.of(dto);
                                    orgBranch.setCreatedBy(headers.get(X_IDENTITY));
                                    return setOrgId(headers, dto, orgBranch);
                                }));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_BRANCH_CREATION);
                });
            } else {
                return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_BRANCH_CREATION);
            }
        }));
    }

    private Mono<WsResponse> saveBranch(Map<String, String> headers, OrgBranchEntity orgBranch) {
        return orgBranchesRepository.save(orgBranch)
                .flatMap(orgBranch1 -> responseMapper.setApiResponse(ERR_SUCCESS, orgBranch1.toBranchResponse(), TRANS_BRANCH_CREATION, FALSE, headers));
    }

    private Mono<WsResponse> setOrgId(Map<String, String> headers, OrgBranchDto dto, OrgBranchEntity orgBranch) {
        return orgRepository.findById(dto.getOrgId())
                .flatMap(orgEntity -> {
                    orgBranch.setOrgId(dto.getOrgId());
                    return saveBranch(headers, orgBranch);
                })
                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Invalid Organisation id.", ERR_VALIDATION, TRANS_BRANCH_CREATION)));
    }

    @Override
    public Mono<WsResponse> fetchById(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return orgBranchesRepository.findById(Long.valueOf(headers.get("org-branch-id")))
                        .flatMap(orgBranch -> responseMapper.setApiResponse(ERR_SUCCESS, orgBranch.toBranchResponse(), TRANS_BRANCH_FETCH, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_BRANCH_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> fetchByOrgId(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return orgBranchesRepository.findByOrgId(headers.get("org-id")).collectList()
                        .map(MsStarterVariables::getBranchResponse)
                        .flatMap(orgBranch -> responseMapper.setApiResponse(ERR_SUCCESS, orgBranch, TRANS_BRANCH_FETCH, FALSE, headers));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_BRANCH_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> fetchByBranchName(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return orgBranchesRepository.findByBranchName(headers.get("branch-name"))
                        .flatMap(orgBranch -> responseMapper.setApiResponse(ERR_SUCCESS, orgBranch.toBranchResponse(), TRANS_BRANCH_FETCH, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_BRANCH_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize) {
        return shared.pagingValidation(pageSize)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(e -> {
                            if (e.getErrorMessage() == null) {
                                return orgBranchesRepository.findBy(getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                        .map(MsStarterVariables::getBranchResponse)
                                        .zipWith(orgBranchesRepository.count())
                                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                                        .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_BRANCH_FETCH, FALSE, headers));
                            } else
                                return shared.customResponse(headers, e.getErrorMessage(), TRANS_BRANCH_FETCH);
                        });
                    } else {
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_BRANCH_FETCH);
                    }
                });
    }

}
