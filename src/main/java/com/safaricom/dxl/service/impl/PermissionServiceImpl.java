package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.PermissionDto;
import com.safaricom.dxl.data.entities.PermissionEntity;
import com.safaricom.dxl.data.pojos.PaginationList;
import com.safaricom.dxl.data.pojos.PermissionResponse;
import com.safaricom.dxl.data.repositories.PermissionRepository;
import com.safaricom.dxl.service.PermissionService;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.FALSE;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.X_IDENTITY;

@Service
@AllArgsConstructor
public class PermissionServiceImpl implements PermissionService {
    private final PermissionRepository repository;
    private final Shared shared;
    private final WsResponseMapper responseMapper;
    private final SSOToken ssoToken;

    @Override
    public Mono<WsResponse> add(Map<String, String> headers, Mono<PermissionDto> payload) {
        return payload.flatMap(dto -> shared.validatePermission(dto).flatMap(e -> {
            if (e.getErrorMessage() == null) {
                return ssoToken.validation(headers).flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return repository.findByPermission(dto.getPermission())
                                .flatMap(permissionEntity -> responseMapper.setApiResponse(ERR_SUCCESS, permissionEntity.toPermissionResponse(), TRANS_PERMISSION_CREATION, FALSE, headers))
                                .switchIfEmpty(Mono.defer(() -> {
                                    PermissionEntity permission = PermissionEntity.of(dto);
                                    permission.setCreatedBy(headers.get(X_IDENTITY));
                                    return repository.save(permission.setAsNew())
                                            .flatMap(permissionEntity -> responseMapper.setApiResponse(ERR_SUCCESS, permissionEntity.toPermissionResponse(), TRANS_PERMISSION_CREATION, FALSE, headers));
                                }));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_PERMISSION_CREATION);
                });
            } else
                return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_PERMISSION_CREATION);
        }));
    }


    @Override
    public Mono<WsResponse> fetchById(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                return repository.findById(headers.get("permission-id"))
                        .flatMap(permissionEntity -> responseMapper.setApiResponse(ERR_SUCCESS, permissionEntity.toPermissionResponse(), TRANS_FETCH_PERMISSION, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_FETCH_PERMISSION);
        });
    }

    @Override
    public Mono<WsResponse> fetchByPermission(Map<String, String> headers) {
        return ssoToken.validation(headers).flatMap(e -> {
            if (e.getErrorMessage() == null) {
                return repository.findByPermission(headers.get("permission"))
                        .flatMap(permission -> responseMapper.setApiResponse(ERR_SUCCESS, permission.toPermissionResponse(), TRANS_FETCH_PERMISSION, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, e.getErrorMessage(), TRANS_FETCH_PERMISSION);
        });
    }

    @Override
    public Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize) {
        return shared.pagingValidation(pageSize)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(e -> {
                            if (e.getErrorMessage() == null) {
                                return repository.findBy(getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                        .map(PermissionServiceImpl::getPermissionResponses)
                                        .zipWith(repository.count())
                                        .map(tuple -> new PaginationList(tuple.getT2(), tuple.getT1()))
                                        .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_FETCH_PERMISSION, FALSE, headers));
                            } else
                                return shared.customResponse(headers, e.getErrorMessage(), TRANS_FETCH_PERMISSION);
                        });
                    } else {
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_FETCH_PERMISSION);
                    }
                });
    }

    private static List<PermissionResponse> getPermissionResponses(List<PermissionEntity> permissionEntities) {
        List<PermissionResponse> permissionResponseList = new ArrayList<>();
        permissionEntities.forEach(permissionEntity -> permissionResponseList.add(permissionEntity.toPermissionResponse()));
        return permissionResponseList;
    }

}