package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.data.entities.OutletTypeEntity;
import com.safaricom.dxl.data.pojos.OutletTypeResponse;
import com.safaricom.dxl.data.repositories.OrganizationTypeRepository;
import com.safaricom.dxl.data.repositories.OutletTypeRepository;
import com.safaricom.dxl.exception.DuplicateRecordException;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.mapper.LogMapper;
import com.safaricom.dxl.service.OutletTypeService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Shared.returnChainedException;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@RequiredArgsConstructor
public class OutletTypeServiceImpl implements OutletTypeService {
    public static final String NOT_FOUND = "Not Found";
    private static final Logger log = LoggerFactory.getLogger(OutletTypeServiceImpl.class);
    private final OutletTypeRepository outletTypeRepository;
    private final OrganizationTypeRepository organizationTypeRepository;
    private final WsResponseMapper responseMapper;

    @Override
    public Mono<WsResponse> create(final OutletTypeDto dto, Map<String, String> headers) {
        return organizationTypeRepository.findById(dto.getOrganizationTypeId())
                .switchIfEmpty(Mono.error(new NotFoundException("OrgType id - ".concat(String.valueOf(dto.getOrganizationTypeId())).concat(" Not Found"), ERR_NOT_FOUND)))
                .flatMap(orgType -> outletTypeRepository.findByDealerName(dto.getDealerName().trim())
                        .flatMap(outletTypeEntity -> Mono.<WsResponse>error(new DuplicateRecordException("Dealer '" + dto.getDealerName() + "' already exists in the '" + orgType.getName() + "' Organization", ERR_CONFLICT)))
                        .switchIfEmpty(Mono.defer(() -> saveOutletType(dto, headers)))
                        .onErrorResume(throwable -> {
                                    if (returnChainedException(throwable)) {
                                        return Mono.error(throwable);
                                    }
                                    return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_WRITE));
                                }
                        )
                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_WRITE));
                        }
                );
    }

    private Mono<WsResponse> saveOutletType(OutletTypeDto dto, Map<String, String> headers) {
        OutletTypeEntity entity = OutletTypeEntity.builder()
                .dealerName(dto.getDealerName().trim().toUpperCase())
                .description(dto.getDescription().trim())
                .organizationTypeId(dto.getOrganizationTypeId())
                .created(LocalDateTime.now())
                .createdBy(headers.get(X_IDENTITY))
                .build();
        return outletTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_CREATED, organizationTypeResponse, TRANS_POST_OUTLET_CREATION, FALSE, headers))
                .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_WRITE)));
    }

    @Override
    public Mono<WsResponse> update(final Long id, final OutletTypeDto dto, Map<String, String> headers) {
        return outletTypeRepository.findById(id)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType ".concat(String.valueOf(id)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(existing -> updateOutletType(dto, existing, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    private Mono<WsResponse> updateOutletType(OutletTypeDto dto, OutletTypeEntity entity, Map<String, String> headers) {
        LogMapper.INSTANCE.mapOutletType(dto, entity);
        entity.setUpdatedBy(headers.get(X_IDENTITY));
        entity.setUpdated(LocalDateTime.now());
        return outletTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponse, TRANS_PUT_OUTLET_UPDATE, FALSE, headers))
                .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_UPDATE)));
    }

    @Override
    public Mono<WsResponse> delete(final Long id, Map<String, String> headers) {
        //Ensure the record by submitted id exists
        return outletTypeRepository.findById(id)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType id - ".concat(String.valueOf(id)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(existing -> outletTypeRepository.deleteById(id)
                        .doOnSuccess(v -> log.info("Deleted OutletType id={}", id))
                        .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_DEL_OUTLET_DELETE, FALSE, headers))
                        .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_DELETE)))
                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getById(final Long id, Map<String, String> headers) {
        return outletTypeRepository.findById(id)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType id - ".concat(String.valueOf(id)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .map(this::toResponse)
                .flatMap(outletTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponse, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getAll(Map<String, String> headers) {
        return outletTypeRepository.findAll()
                .switchIfEmpty(Mono.error(new NotFoundException("OutletTypes is empty", ERR_NOT_FOUND)))
                .map(this::toResponse)
                .collectList()
                .flatMap(outletTypeResponses -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponses, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getByOrganizationTypeId(final Long organizationTypeId, Map<String, String> headers) {
        return outletTypeRepository.findByOrganizationTypeId(organizationTypeId)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletTypes not found", ERR_NOT_FOUND)))
                .map(this::toResponse)
                .collectList()
                .flatMap(outletTypeResponses -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponses, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getMessage(), ERR_DB_READ));
                        }
                );
    }

    private OutletTypeResponse toResponse(OutletTypeEntity entity) {
        return OutletTypeResponse.builder()
                .id(entity.getId())
                .dealerName(entity.getDealerName())
                .description(entity.getDescription())
                .organizationTypeId(entity.getOrganizationTypeId())
                .build();
    }
}
