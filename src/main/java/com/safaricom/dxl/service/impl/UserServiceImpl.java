package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.RoleAndCodeDto;
import com.safaricom.dxl.data.dto.StatusAndRoleAndCodeDto;
import com.safaricom.dxl.data.dto.UserDto;
import com.safaricom.dxl.data.entities.OrgEntity;
import com.safaricom.dxl.data.entities.UserOnBoardLogsEntity;
import com.safaricom.dxl.data.entities.UsersEntity;
import com.safaricom.dxl.data.entities.UsersFailedRequestEntity;
import com.safaricom.dxl.data.pojos.ErrorMapping;
import com.safaricom.dxl.data.repositories.*;
import com.safaricom.dxl.service.UserService;
import com.safaricom.dxl.utils.Iprs;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.utils.UserOnboard;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

/**
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class UserServiceImpl implements UserService {
    private final WsResponseMapper responseMapper;
    private final ClustersRepository clustersRepository;
    private final RoleRepository rolesRepository;
    private final UsersRepository usersRepository;
    private final OrgRepository orgRepository;
    @Lazy
    private final Shared shared;
    @Lazy
    private final SSOToken ssoToken;
    @Lazy
    private final UserRepositoryPort userRepositoryPort;
    private final UserFailedRequestRepository userFailedRequestRepository;
    @Lazy
    private final Iprs iprs;
    private final UserOnboard userOnboard;
    private final UserOnBoardLogsRepository userOnBoardLogsRepository;

    /**
     *
     * @param headers
     * @param payload
     * @return
     */
    @Override
    public Mono<WsResponse> createUser(Map<String, String> headers, Mono<UserDto> payload) {
        return payload
                .flatMap(dto -> shared.validateUser(dto).flatMap(e -> {
                    if (e.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(errorMapping -> {
                            if (errorMapping.getErrorMessage() == null)
                                return processCreateUser(headers, dto);
                            else
                                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_CREATION);
                        });
                    } else
                        return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
                }))
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "createUser", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    private Mono<WsResponse> processCreateUser(Map<String, String> headers, UserDto dto) {
        return iprs.validate(headers, dto, true)
                .flatMap(iprsStats -> usersRepository.findByEmail(dto.getEmail())
                                .flatMap(savedUser -> {
                                    if (ALLOWED_SSO_REGISTRATION.contains(savedUser.getRegistrationStatus()) && savedUser.isEnabled())
                                        return shared.customResponse(headers, "A user with this email address already exists. Please use a different address to register your account.", ERR_BAD_REQUEST, TRANS_USER_UPDATE);
                                    if (ALLOWED_SSO_REGISTRATION.contains(iprsStats.getStatus())) {
                                        return userFailedRequestRepository.save(UsersFailedRequestEntity.of(savedUser))
                                                .doOnSuccess(usersFailedRequestEntity -> usersRepository.delete(savedUser).subscribe())
                                                .flatMap(usersFailedRequestEntity -> {
                                                    UsersEntity newUser = UsersEntity.of(dto, savedUser.getCodes());
                                                    setStatus(headers, dto, iprsStats, newUser);
                                                    newUser.setUpdated(localDateTime());
                                                    newUser.setUpdatedBy(headers.get(X_IDENTITY));
                                                    UsersEntity encryptedUser = shared.encryptUser(newUser);
                                                    return setRole(headers, dto, encryptedUser);
                                                });
                                    }
                                    return shared.customResponse(headers, "Invalid details. Ensure you enter a valid name, document number and phone number", ERR_BAD_REQUEST, TRANS_USER_UPDATE);
                                })
                                .switchIfEmpty(Mono.defer(() -> {
                                    UsersEntity newUser = UsersEntity.of(dto, null);
                                    setStatus(headers, dto, iprsStats, newUser);
                                    UsersEntity encryptedUser = shared.encryptUser(newUser);
                                    return setRole(headers, dto, encryptedUser);
                                })));
    }

    /**
     *
     * @param headers
     * @return WsResponse
     */
    @Override
    public Mono<WsResponse> fetchUserByEmail(Map<String, String> headers) {
        return ssoToken.validation(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return usersRepository.findByEmail(headers.get(EMAIL).toLowerCase())
                                .flatMap(usersEntity -> processRegions(headers, usersEntity))
                                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Not Found", ERR_NOT_FOUND, TRANS_USER_FETCH)));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchUserByEmail", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    /**
     *
     * @param headers
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize) {
        return ssoToken.validation(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return shared.pagingValidation(pageSize)
                                .flatMap(e -> {
                                    if (e.getErrorMessage() == null) {
                                        return usersRepository.findBy(getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                                .zipWith(usersRepository.count())
                                                .flatMap(shared::getUserListPaginated)
                                                .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_USER_FETCH, FALSE, headers))
                                                .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
                                    } else
                                        return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_FETCH);
                                });
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchAllPaginated", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    /**
     *
     * @param headers
     * @return
     */
    @Override
    public Mono<WsResponse> enableUser(Map<String, String> headers) {
        return shared.validateSession(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return usersRepository.findByEmail(headers.get(EMAIL).trim())
                                .flatMap(user -> {
                                    if (user.isEnabled())
                                        return responseMapper.setApiResponse(ERR_SUCCESS, user.toRegResponse(null), TRANS_USER_UPDATE, FALSE, headers);
                                    else {
                                        if (ALLOWED_SSO_REGISTRATION.contains(user.getRegistrationStatus())) {
                                            user.setEnabled(true);
                                            return userRepositoryPort.enableUser(user.getId(), headers.get(X_IDENTITY))
                                                    .doOnSuccess(s -> shared.sendRegistrationRequest(headers, user, true))
                                                    .flatMap(usersEntity -> responseMapper.setApiResponse(ERR_SUCCESS, user.toRegResponse(null), TRANS_USER_UPDATE, FALSE, headers));
                                        }
                                        return shared.customResponse(headers, "User registration status validation failed.", ERR_VALIDATION, TRANS_USER_UPDATE);
                                    }
                                })
                                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "User Not Found", ERR_NOT_FOUND, TRANS_USER_UPDATE)));
                    }
                    return shared.customResponse(headers, "Invalid session", "401", TRANS_USER_UPDATE);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "enableUser", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    /**
     *
     * @param headers
     * @return
     */
    @Override
    public Mono<WsResponse> fetchByCode(Map<String, String> headers) {
        return ssoToken.validation(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return usersRepository.findByCode(headers.get("code").trim()).collectList()
                                .zipWith(usersRepository.countByCode(headers.get("code").trim()))
                                .flatMap(shared::getUserListPaginated)
                                .flatMap(paginationListMono -> responseMapper.setApiResponse(ERR_SUCCESS, paginationListMono, TRANS_USER_FETCH, FALSE, headers))
                                .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchByCode", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    /**
     *
     * @param headers
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Mono<WsResponse> fetchByRole(Map<String, String> headers, int pageNo, int pageSize) {
        return shared.pagingValidation(pageSize)
                .flatMap(e -> {
                            if (e.getErrorMessage() == null){
                                return ssoToken.validation(headers).flatMap(errorMapping -> {
                                    if (errorMapping.getErrorMessage() == null) {
                                        return usersRepository.findByRole(headers.get("role"), getListSize(pageNo, pageSize, Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                                .zipWith(usersRepository.countByRole(headers.get("role")))
                                                .flatMap(shared::getUserListPaginated)
                                                .flatMap(paginationListMono -> responseMapper.setApiResponse(ERR_SUCCESS, paginationListMono, TRANS_USER_FETCH, FALSE, headers))
                                                .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
                                    } else
                                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                                });
                            } else
                                return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchByRole", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    @Override
    public Mono<WsResponse> fetchByRoleAndCode(Map<String, String> headers, Mono<RoleAndCodeDto> payload) {
        return payload
                .flatMap(dto -> ssoToken.validation(headers).flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return usersRepository.findByRoleAndCodes(dto.getRole(), dto.getCode(), pageNumber(dto.getPageNo()), dto.getPageSize()).collectList()
                                .zipWith(usersRepository.countByRoleAndCodes(dto.getRole(), dto.getCode()))
                                .flatMap(shared::getUserListPaginated)
                                .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_USER_FETCH, FALSE, headers))
                                .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                }))
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchByRoleAndCode", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    @Override
    public Mono<WsResponse> fetchUserByEmailAndRoleHierarchy(Map<String, String> headers) {
        return shared.validateRoleAndEmail(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(e -> {
                            if (e.getErrorMessage() == null){
                                return usersRepository.findByEmail(headers.get(EMAIL).trim())
                                        .flatMap(usersEntity -> usersRepository.findByRoleIn(shared.getRoleNamesUnder(usersEntity.getRole()), getListSize(Integer.parseInt(headers.get("page-no")), Integer.parseInt(headers.get("page-size")), Sort.by(Sort.Direction.DESC, CREATED))).collectList()
                                                .zipWith(usersRepository.countByRoleIn(shared.getRoleNamesUnder(usersEntity.getRole())))
                                                .flatMap(shared::getUserListPaginated)
                                                .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_USER_FETCH, FALSE, headers)))
                                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "User not found", ERR_NOT_FOUND, TRANS_USER_FETCH)));
                            } else
                                return shared.customResponse(headers, e.getErrorMessage(), TRANS_USER_FETCH);
                        });
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "fetchUserByEmailAndRoleHierarchy", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    @Override
    public Mono<WsResponse> loginTime(Map<String, String> headers) {
        return ssoToken.validation(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return usersRepository.findByEmail(headers.get(EMAIL))
                                .flatMap(usersEntity -> {
                                    usersEntity.setLoginTime(localDateTime());
                                    usersEntity.setUpdated(localDateTime());
                                    usersEntity.setUpdatedBy(headers.get(X_IDENTITY));
                                    return userRepositoryPort.updateLoginTime(usersEntity.getId(), headers.get(X_IDENTITY))
                                            .flatMap(success -> shared.returnUserDetails(headers, usersEntity, TRANS_USER_UPDATE));
                                })
                                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "User not found.", ERR_NOT_FOUND, TRANS_USER_UPDATE)));
                    } else
                        return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "loginTime", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }


    @Override
    public Mono<WsResponse> fetchOrgUsers(Map<String, String> headers) {
        ErrorMapping validateCode = shared.validateCode(headers.get("code"));
        if (validateCode.getErrorMessage() == null){
            return ssoToken.validation(headers).flatMap(errorMapping -> {
                if (errorMapping.getErrorMessage() == null) {
                    return usersRepository.findByCode(headers.get("code")).collectList()
                            .zipWith(usersRepository.countByCode(headers.get("code")))
                            .flatMap(shared::getUserListPaginated)
                            .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_USER_FETCH, FALSE, headers));
                }
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
            });
        }
        return shared.customResponse(headers, validateCode.getErrorMessage(), ERR_VALIDATION, TRANS_USER_FETCH);
    }

    @Override
    public Mono<WsResponse> registration(Map<String, String> headers, Mono<UserDto> payload) {
        return payload
                .flatMap(dto -> shared.validateRegistration(dto)
                        .flatMap(e -> {
                            if (e.getErrorMessage() == null)
                                return userOnboard.processRegistrationRequest(headers, dto);
                            else {
                                userOnBoardLogsRepository.save(UserOnBoardLogsEntity.of(dto, ERR_VALIDATION, e.getErrorMessage(), headers.get(X_IDENTITY))).subscribe();
                                return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
                            }
                        }))
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "registration", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    @Override
    public Mono<WsResponse> deactivateUser(Map<String, String> headers) {
        return shared.validateUserEmail(headers)
                .flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        return ssoToken.validation(headers).flatMap(e -> {
                            if (e.getErrorMessage() == null) {
                                return usersRepository.findByEmail(headers.get("logged-in-user-email").toLowerCase())
                                        .flatMap(loggedInUser -> usersRepository.findByEmailAndRoleIn(headers.get("email").trim().toLowerCase(), shared.getRoleHierarchy(loggedInUser.getRole()))
                                                .flatMap(usersEntity -> {
                                                    if (usersEntity.isEnabled()) {
                                                        return userRepositoryPort.disableUser(usersEntity.getId(), headers.get(X_IDENTITY))
                                                                .flatMap(y -> responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_USER_UPDATE, FALSE, headers));
                                                    }
                                                    return shared.customResponse(headers, "User already disabled", "304", TRANS_USER_FETCH);
                                                })
                                                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "You are only allowed to disabled users under you.", ERR_NOT_FOUND, TRANS_USER_FETCH))))
                                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "User not found", ERR_NOT_FOUND, TRANS_USER_FETCH)));
                            }
                            return shared.customResponse(headers, e.getErrorMessage(), TRANS_USER_FETCH);
                        });
                    }
                    return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_USER_UPDATE);
                })
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "deactivate user", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));
    }

    @Override
    public Mono<WsResponse> updateUserOtp(Map<String, String> headers, Mono<UserDto> payload) {
        return payload
                .flatMap(dto -> shared.validatePhone(dto).flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null) {
                        return ssoToken.validation(headers).flatMap(e -> {
                            if (e.getErrorMessage() == null) {
                                return usersRepository.findByEmail(dto.getEditorEmail())
                                        .flatMap(superuser -> {
                                            if (superuser.getRole().equalsIgnoreCase("Super User"))
                                                return shared.processUpdateOtpRequest(headers, dto);
                                            return shared.customResponse(headers, "Editor must be super user.", ERR_BAD_REQUEST, TRANS_USER_FETCH);
                                        })
                                        .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Super user not found.", ERR_NOT_FOUND, TRANS_USER_FETCH)));
                            }
                            return shared.customResponse(headers, e.getErrorMessage(), TRANS_USER_FETCH);
                        });
                    }
                    return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_USER_UPDATE);
                }))
                .doOnError(err -> starterError(headers.get(X_CONVERSATION_ID), "Update OTP", "", err.getMessage()))
                .onErrorResume(throwable -> shared.customResponse(headers, REQUEST_FAILED, ERR_BAD_REQUEST, TRANS_USER_CREATION));

    }

    /**
     *
     * @param headers
     * @param payload
     * @return
     */
    @Override
    public Mono<WsResponse> fetchByStatusAndRoleAndCode(Map<String, String> headers, Mono<StatusAndRoleAndCodeDto> payload) {
        return payload.flatMap(dto -> shared.validateRoleCodeDto(dto)
                .flatMap(e -> {
                    if (e.getErrorMessage() == null)
                        return processFetchStatusAndRoleAndCode(headers, dto);
                    else
                        return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_FETCH);
                }));
    }

    /**
     *
     * @param headers
     * @return
     */
    @Override
    public Mono<WsResponse> deLinkCodeFromUser(Map<String, String> headers) {
                return ssoToken.validation(headers).flatMap(tokenError -> {
                    if (tokenError.getErrorMessage() == null){
                        return usersRepository.findByEmail(headers.get(EMAIL))
                                .flatMap(usersEntity -> shared.processDeLinkCodeFromUser(headers, usersEntity))
                                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Not found", ERR_NOT_FOUND, TRANS_USER_UPDATE)));
                    }
                    return shared.customResponse(headers, tokenError.getErrorMessage(), TRANS_USER_UPDATE);
                });
    }

    private Mono<WsResponse> processFetchStatusAndRoleAndCode(Map<String, String> headers, StatusAndRoleAndCodeDto dto) {
        return ssoToken.validation(headers).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null) {
                boolean isEnabled = dto.getStatus().equalsIgnoreCase(ACTIVE);
                return usersRepository.findByIsEnabledAndRoleAndCodes(isEnabled, dto.getRole(), dto.getCode(), pageNumber(dto.getPageNo()), dto.getPageSize()).collectList()
                        .zipWith(usersRepository.countByIsEnabledAndRoleAndCodes(isEnabled, dto.getRole(), dto.getCode()))
                        .flatMap(shared::getUserListPaginated)
                        .flatMap(paginationList -> responseMapper.setApiResponse(ERR_SUCCESS, paginationList, TRANS_USER_FETCH, FALSE, headers))
                        .switchIfEmpty(Mono.defer(() -> Mono.error(new WsResourceNotFoundException())));
            } else
                return shared.customResponse(headers, errorMapping.getErrorMessage(), TRANS_USER_FETCH);
        });
    }

    @Override
    public Mono<WsResponse> logout(Map<String, String> headers) {
        ssoToken.logout(headers);
        return responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_USER_LOGOUT, FALSE, headers);
    }

    /**
     *
     * @param headers
     * @param user
     * @return
     */
    private Mono<WsResponse> processRegions(Map<String, String> headers, UsersEntity user) {
        List<Object> objectList = new ArrayList<>();
        if (user.getTerritory() != null){
            return clustersRepository.findByTerritory(user.getTerritory().toString()).collectList()
                    .flatMap(clustersEntities -> {
                        clustersEntities.forEach(clusters -> objectList.add(clusters.getCluster()));
                        user.setTerritory(null);
                        user.setRegion(null);
                        user.setClusters(objectList);
                        return shared.returnUserDetails(headers, user, TRANS_USER_FETCH);

                    });
        } else if (user.getRegion() != null){
            return clustersRepository.findByRegion(user.getRegion().toString()).collectList()
                    .flatMap(clustersEntities -> {
                        clustersEntities.forEach(clusters -> objectList.add(clusters.getTerritory()));
                        user.setRegion(null);
                        user.setClusters(null);
                        user.setTerritory(objectList);
                        return shared.returnUserDetails(headers, user, TRANS_USER_FETCH);
                    });
        } else
            return shared.returnUserDetails(headers, user, TRANS_USER_CREATION);
    }

    /**
     *
     * @param headers
     * @param rawUser
     * @param usersEntity
     * @return
     */
    private Mono<WsResponse> setRole(Map<String, String> headers, UserDto rawUser, UsersEntity usersEntity) {
        return rolesRepository.findByRoleName(rawUser.getRole())
                .flatMap(roleEntity -> shared.validateSafEmail(usersEntity.getEmail(), roleEntity.getCategory()).flatMap(errorMapping -> {
                    if (errorMapping.getErrorMessage() == null){
                        usersEntity.setRole(roleEntity.getRoleName());
                        return setOrg(headers, rawUser, usersEntity);
                    }
                    return shared.customResponse(headers, errorMapping.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
                }))
                .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, INVALID_ROLE, ERR_VALIDATION, TRANS_USER_CREATION)));
    }

    /**
     *
     * @param headers
     * @param dto
     * @param user
     * @return
     */
    private Mono<WsResponse> setOrg(Map<String, String> headers, UserDto dto, UsersEntity user) {
        return shared.validateCodeString(dto.getCode()).flatMap(errorMapping -> {
            if (errorMapping.getErrorMessage() == null){
                ErrorMapping e = shared.validateCode(dto.getCode());
                if (e.getErrorMessage() == null) {
                    return orgRepository.findByCode(dto.getCode())
                            .flatMap(orgEntity -> processCodeForOrg(headers, dto, user, orgEntity))
                            .switchIfEmpty(Mono.defer(() -> shared.customResponse(headers, "Kindly enter a valid organization code.", ERR_VALIDATION, TRANS_USER_CREATION)));
                } else
                    return shared.customResponse(headers, e.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
            } else
                return saveUser(headers, user, dto.isUpdate());
        });
    }

    private Mono<WsResponse> processCodeForOrg(Map<String, String> headers, UserDto dto, UsersEntity user, OrgEntity orgEntity) {
        if (user.getCodes() == null)
            user.setCodes(Set.of(orgEntity.getCode()));
        else
            user.getCodes().add(orgEntity.getCode());
        //For users who are directors, confirm if their kyc is approved up to second level apart from PCC.
        return userOnboard.validateDirectorKycApprovalOld(user, orgEntity)
                .flatMap(kycError -> {
                    if (kycError.getErrorMessage() == null)
                        return saveUser(headers, user, dto.isUpdate());
                    return shared.customResponse(headers, kycError.getErrorMessage(), ERR_VALIDATION, TRANS_USER_CREATION);
                });
    }

    private Mono<WsResponse> saveUser(Map<String, String> headers, UsersEntity user, boolean isUpdate) {
        if (isUpdate) {
            return userRepositoryPort.updateUser(user, headers.get(X_IDENTITY)).flatMap(success -> shared.returnUserDetails(headers, user, TRANS_USER_UPDATE));
        } else {
            return usersRepository.save(user)
                    .doOnSuccess(userSuccess -> shared.sendRegistrationRequest(headers, user, true))
                    .flatMap(returnUser -> shared.returnUserDetails(headers, user, TRANS_USER_CREATION));
        }
    }
}
