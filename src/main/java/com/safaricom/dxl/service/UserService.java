package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.RoleAndCodeDto;
import com.safaricom.dxl.data.dto.StatusAndRoleAndCodeDto;
import com.safaricom.dxl.data.dto.UserDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public interface UserService {

    Mono<WsResponse> createUser(Map<String, String> headers, Mono<UserDto> payload);

    Mono<WsResponse> fetchUserByEmail(Map<String, String> headers);

    Mono<WsResponse> fetchAllPaginated(Map<String, String> headers, int pageNo, int pageSize);

    Mono<WsResponse> enableUser(Map<String, String> headers);

    Mono<WsResponse> fetchByCode(Map<String, String> headers);

    Mono<WsResponse> fetchByRole(Map<String, String> headers, int pageNo, int pageSize);

    Mono<WsResponse> fetchByStatusAndRoleAndCode(Map<String, String> headers, Mono<StatusAndRoleAndCodeDto> payload);

    Mono<WsResponse> deLinkCodeFromUser(Map<String, String> headers);

    Mono<WsResponse> logout(Map<String, String> headers);

    Mono<WsResponse> fetchByRoleAndCode(Map<String, String> headers, Mono<RoleAndCodeDto> payload);

    Mono<WsResponse> fetchUserByEmailAndRoleHierarchy(Map<String, String> headers);

    Mono<WsResponse> loginTime(Map<String, String> headers);

    Mono<WsResponse> fetchOrgUsers(Map<String, String> headers);

    Mono<WsResponse> registration(Map<String, String> headers, Mono<UserDto> payload);

    Mono<WsResponse> deactivateUser(Map<String, String> headers);

    Mono<WsResponse> updateUserOtp(Map<String, String> headers, Mono<UserDto> payload);
}