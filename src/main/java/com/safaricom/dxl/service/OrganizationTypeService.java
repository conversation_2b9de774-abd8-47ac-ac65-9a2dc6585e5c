package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.OrganizationTypeDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Service interface for managing Organization Types.
 */
public interface OrganizationTypeService {
    Mono<WsResponse> create(OrganizationTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> update(Long id, OrganizationTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> delete(Long id, Map<String, String> headers);
    Mono<WsResponse> getById(Long id, Map<String, String> headers);
    Mono<WsResponse> getAll(Map<String, String> headers);
    Mono<WsResponse> getAllWithOutlets(Map<String, String> headers);
}
