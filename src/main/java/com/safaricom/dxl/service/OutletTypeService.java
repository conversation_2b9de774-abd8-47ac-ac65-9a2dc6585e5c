package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Service interface for managing Outlet Types.
 */
public interface OutletTypeService {
    Mono<WsResponse> create(OutletTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> update(Long id, OutletTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> delete(Long id, Map<String, String> headers);
    Mono<WsResponse> getById(Long id, Map<String, String> headers);
    Mono<WsResponse> getAll(Map<String, String> headers);
    Mono<WsResponse> getByOrganizationTypeId(Long organizationTypeId, Map<String, String> headers);
}
