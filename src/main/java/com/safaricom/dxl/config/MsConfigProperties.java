package com.safaricom.dxl.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "dxl.ms")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MsConfigProperties {
    private String key;
    private String initializationVector;

    private String xMessageId;
    private String xDeviceId;
    private String tokenUrl;
    private String tokenAuth;
    private String subRegUrl;
    private String subRegAuth;
    private String governmentApiUrl;
    private String governmentApiAuth;
    private String xApiKey;
    private String kmsKeyId;
    private String rolesUnderSuperUser;
    private String rolesUnderRtcPartnerSupport;
    private String rolesUnderDirector;
    private String rolesUnderAdministrator;

    private String transTokenApi;
    private String transIdentity;
    private String countryCode;
    private String division;
    private String operator;
    private String apiKey;
    private String sourceSystem;
    private String partnerCodeError;
    private String kycApprovalMsg;
    private String kycDirectorErrorMsg;
    private String kycMissingErrorMsg;

    private boolean gkIprs;
    private int ttl;

    private String basicAuth;

    private String invalidCodeMsg;
    private String userAlreadyExistsMsg;
    private String documentTypeMsg;

    private String noDirectorErrorMsg;
    private boolean enableStreaming;
}