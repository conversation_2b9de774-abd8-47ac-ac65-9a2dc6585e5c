package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.RoleEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collection;

@Repository
public interface RoleRepository extends R2dbcRepository<RoleEntity, Long> {
    Mono<RoleEntity> findByCategoryAndRoleName(String category, String role);

    Mono<RoleEntity> findByRoleName(String roleName);

    Flux<RoleEntity> findByCategory(String category);
    Flux<RoleEntity> findBy(Pageable pageable);

    Flux<RoleEntity> findByRoleNameIn(Collection<String> roleNames);

}
