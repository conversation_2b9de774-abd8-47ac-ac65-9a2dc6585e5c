package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.OrganizationTypeEntity;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface OrganizationTypeRepository extends R2dbcRepository<OrganizationTypeEntity, Long> {
    Mono<OrganizationTypeEntity> findByName(String name);
}
