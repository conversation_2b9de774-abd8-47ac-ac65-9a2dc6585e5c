package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.OrgEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Set;

public interface OrgRepository extends R2dbcRepository<OrgEntity, Long> {
    Flux<OrgEntity> findByOrgName(String orgName);
    Mono<OrgEntity> findByCode(String code);
    Flux<OrgEntity> findBy(Pageable pageable);
    Flux<OrgEntity> findByCodeIn(Set<String> codes);
    Mono<OrgEntity> findByHoStoreNumber(String code);
}
