package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.ClustersEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface ClustersRepository extends R2dbcRepository<ClustersEntity, Long> {
    Mono<ClustersEntity> findByRegionAndTerritoryAndCluster(String region, String territory, String cluster);
    Flux<ClustersEntity> findByRegion(String region);
    Flux<ClustersEntity> findByTerritory(String territory);
    Flux<ClustersEntity> findByCluster(String cluster);
    Flux<ClustersEntity> findBy(Pageable pageable);
}
