package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.OutletTypeEntity;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface OutletTypeRepository extends R2dbcRepository<OutletTypeEntity, Long> {
    Flux<OutletTypeEntity> findByOrganizationTypeId(Long organizationTypeId);

    Mono<OutletTypeEntity> findByDealerCode(String dealerCode);
}
