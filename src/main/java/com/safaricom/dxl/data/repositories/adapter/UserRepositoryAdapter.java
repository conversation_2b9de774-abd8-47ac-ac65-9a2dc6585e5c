package com.safaricom.dxl.data.repositories.adapter;

import com.safaricom.dxl.data.entities.UsersEntity;
import com.safaricom.dxl.data.repositories.UserRepositoryPort;
import lombok.AllArgsConstructor;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Set;

import static com.safaricom.dxl.utils.MsStarterVariables.localDateTime;
import static org.springframework.data.relational.core.query.Criteria.where;

@Component
@AllArgsConstructor
public class  UserRepositoryAdapter implements UserRepositoryPort {
    private final R2dbcEntityTemplate template;

    @Override
    public Mono<Long> updateUser(UsersEntity usersEntity, String identity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(usersEntity.getId())))
                .apply(
                        Update
                                .update("id_type", usersEntity.getIdType())
                                .set("registration_doc_id", usersEntity.getRegistrationDocId())
                                .set("phone", usersEntity.getPhone())
                                .set("first_name", usersEntity.getFirstName())
                                .set("last_name", usersEntity.getLastName())
                                .set("registration_status", usersEntity.getRegistrationStatus())
                                .set("registration_remarks", usersEntity.getRegistrationRemarks())
                                .set("updated", usersEntity.getUpdated())
                                .set("updated_by", identity)
                                .set("source_system", usersEntity.getSourceSystem())
                                .set("role", usersEntity.getRole())
                                .set("clusters", usersEntity.getClusters())
                                .set("territory", usersEntity.getTerritory())
                                .set("region", usersEntity.getRegion())
                                .set("codes", usersEntity.getCodes().toArray())
                );
    }

    @Override
    public Mono<Long> updateMsisdn(UsersEntity usersEntity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(usersEntity.getId())))
                .apply(
                        Update
                                .update("phone", usersEntity.getPhone())
                                .set("updated", usersEntity.getUpdated())
                                .set("updated_by", usersEntity.getUpdatedBy())
                );
    }


    @Override
    public Mono<Long> updateLoginTime(Long userId, String identity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(userId)))
                .apply(Update.update("login_time", localDateTime()).set("updated", localDateTime()).set("updated_by", identity));
    }

    @Override
    public Mono<Long> enableUser(Long userId, String identity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(userId)))
                .apply(Update.update("is_enabled", true).set("updated", localDateTime()).set("updated_by", identity));
    }

    @Override
    public Mono<Long> disableUser(Long userId, String identity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(userId)))
                .apply(Update.update("is_enabled", false).set("updated", localDateTime()).set("updated_by", identity));
    }

    @Override
    public Mono<Long> updateCode(Long userId, Set<String> codes, String identity) {
        return this.template.update(UsersEntity.class)
                .matching(Query.query(where("id").is(userId)))
                .apply(Update.update("codes", codes.toArray()).set("updated", localDateTime()).set("updated_by", identity));
    }
}
