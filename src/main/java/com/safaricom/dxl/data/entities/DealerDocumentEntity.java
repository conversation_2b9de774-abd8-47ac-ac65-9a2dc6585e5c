package com.safaricom.dxl.data.entities;

import com.safaricom.dxl.data.enums.DocumentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Table("DEALER_DOCUMENT")
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DealerDocumentEntity {
    @Id
    private Long id;
    private String dealerCode;
    private String documentNumber;
    private DocumentType documentType;
    private String fileName;
    private String filePath;
    private String approvalStatus;
    private String remarks;
    private String riskApprovalStatus;
    private String riskRemarks;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private LocalDateTime riskUpdatedOn;
    private String createdBy;
    private String updatedBy;
    private String riskUpdatedBy;
    private String sourceSystem;
    private String directorId;
    private String comment;
    private LocalDateTime approvedOn;
    private String approvedBy;
    private LocalDateTime riskApprovedOn;
    private String riskApprovedBy;

}
