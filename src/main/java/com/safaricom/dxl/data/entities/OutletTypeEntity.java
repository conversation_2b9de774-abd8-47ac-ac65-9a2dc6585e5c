package com.safaricom.dxl.data.entities;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table("pp_outlet_type")
public class OutletTypeEntity {
    @Id
    private Long id;
    @JsonProperty("dealer_name")
    private String dealerName;
    @JsonProperty("dealer_code")
    private String dealerCode;
    @JsonProperty("ho_code")
    private String hoCode;
    private String description;
    @JsonProperty("organization_type_id")
    private Long organizationTypeId;
    private LocalDateTime created;
    private LocalDateTime updated;
    @JsonProperty("created_by")
    private String createdBy;
    @JsonProperty("updated_by")
    private String updatedBy;
}
