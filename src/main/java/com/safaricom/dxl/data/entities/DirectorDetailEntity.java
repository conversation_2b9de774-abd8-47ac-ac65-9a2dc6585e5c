package com.safaricom.dxl.data.entities;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Table("DIRECTOR_DETAILS")
@ToString
public class DirectorDetailEntity {
    @Id
    String id;
    String mobile;
    String fullName;
    String firstName;
    String lastName;
    String idNumber;
    String nationality;
    String code;
    String role;
    String approvalStatus;
    String approvalMessage;
    String approvedBy;
    LocalDateTime approvedOn;
    LocalDateTime createdOn;
    LocalDateTime updatedOn;
    String otherComment;

    public DirectorDetailEntity(String fullName, String firstName, String lastName) {
        this.fullName = fullName;
        this.firstName = firstName;
        this.lastName = lastName;
    }
}

