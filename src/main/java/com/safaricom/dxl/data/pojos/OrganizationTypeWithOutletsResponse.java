package com.safaricom.dxl.data.pojos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * POJO representing an organization type with all its associated outlet types.
 *
 * @param id          the organization type ID
 * @param name        the organization type dealerName
 * @param description the organization type description
 * @param outletTypes the list of associated outlet types
 */
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrganizationTypeWithOutletsResponse {
    private Long id;
    private String name;
    private String description;
    private List<OutletTypeResponse> outletTypes;
}
