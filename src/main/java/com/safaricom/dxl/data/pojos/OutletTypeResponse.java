package com.safaricom.dxl.data.pojos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

/**
 * Response POJO for Outlet Type.
 */
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OutletTypeResponse {
    private Long id;
    private String dealerName;
    private String dealerCode;
    private String hoCode;
    private String description;
    private Long organizationTypeId;
}
