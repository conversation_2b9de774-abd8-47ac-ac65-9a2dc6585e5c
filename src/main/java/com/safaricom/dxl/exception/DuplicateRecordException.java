package com.safaricom.dxl.exception;

import lombok.Generated;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@Getter
@ResponseStatus(HttpStatus.CONFLICT)
public class DuplicateRecordException extends RuntimeException {
    private final String code;

    public DuplicateRecordException(String message, String code) {
        super(message);
        this.code = code;
    }

    @Generated
    public String getCode() {
        return this.code;
    }

}
