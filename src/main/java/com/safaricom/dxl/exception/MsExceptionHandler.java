package com.safaricom.dxl.exception;

import com.safaricom.dxl.webflux.starter.exception.WsExceptionHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import com.safaricom.dxl.webflux.starter.utils.WsStarterVariables;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

import java.util.Objects;

import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.ES;

/**
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class MsException<PERSON>andler extends WsExceptionHandler {
    
    public MsExceptionHandler(WsMappingService mappingService) {
        super(mappingService);
    }

    @ExceptionHandler({NotFoundException.class})
    protected Mono<ResponseEntity<WsResponse>> notFoundException(NotFoundException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler({DuplicateRecordException.class})
    protected Mono<ResponseEntity<WsResponse>> duplicateRecordException(DuplicateRecordException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.CONFLICT);
    }

    @ExceptionHandler({ForbiddenException.class})
    protected Mono<ResponseEntity<WsResponse>> forbiddenException(ForbiddenException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler({InternalServerErrorException.class})
    protected Mono<ResponseEntity<WsResponse>> internalServerErrorException(InternalServerErrorException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    protected Mono<ResponseEntity<WsResponse>> setErrResponse(String code, String message, ServerHttpRequest request, HttpStatus status) {
        return this.setErrResponse(code, Objects.requireNonNullElse(message, ES), WsStarterVariables.NULL, request.getHeaders(), status, true);
    }

    
}