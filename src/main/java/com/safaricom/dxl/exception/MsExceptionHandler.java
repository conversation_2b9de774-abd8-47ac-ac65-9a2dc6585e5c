package com.safaricom.dxl.exception;

import com.safaricom.dxl.webflux.starter.exception.WsExceptionHandler;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsMappingService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import reactor.core.publisher.Mono;

/**
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class MsExceptionHandler extends WsExceptionHandler {
    
    public MsExceptionHandler(WsMappingService mappingService) {
        super(mappingService);
    }

    @ExceptionHandler({NotFoundException.class})
    protected Mono<ResponseEntity<WsResponse>> notFoundException(NotFoundException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, request.getHeaders(), HttpStatus.NOT_FOUND, true);
    }

    @ExceptionHandler({DuplicateRecordException.class})
    protected Mono<ResponseEntity<WsResponse>> duplicateRecordException(DuplicateRecordException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, request.getHeaders(), HttpStatus.CONFLICT, true);
    }

    @ExceptionHandler({ForbiddenException.class})
    protected Mono<ResponseEntity<WsResponse>> forbiddenException(ForbiddenException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, request.getHeaders(), HttpStatus.FORBIDDEN, true);
    }

    @ExceptionHandler({InternalServerErrorException.class})
    protected Mono<ResponseEntity<WsResponse>> internalServerErrorException(InternalServerErrorException ex, ServerHttpRequest request) {
        return setErrResponse(ex.getCode(), ex.getMessage(), request, request.getHeaders(), HttpStatus.INTERNAL_SERVER_ERROR, true);
    }

    
}