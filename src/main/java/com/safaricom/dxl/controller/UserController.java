package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.RoleAndCodeDto;
import com.safaricom.dxl.data.dto.StatusAndRoleAndCodeDto;
import com.safaricom.dxl.data.dto.UserDto;
import com.safaricom.dxl.service.UserService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Tag(name="User Management", description = "Controller")
@RestController
@RequestMapping("/user")
@AllArgsConstructor
public class UserController {

    private final UserService userService;

    @Operation(summary = "Create user")
    @PostMapping("/")
    public Mono<WsResponse> add(@RequestHeader Map<String, String> headers, @RequestBody Mono<UserDto> payload){
        return userService.createUser(headers, payload);
    }

    @Operation(summary = "Fetch users by email")
    @GetMapping("/")
    public Mono<WsResponse> fetchUserByEmail(@RequestHeader Map<String, String> headers){
        return userService.fetchUserByEmail(headers);
    }

    @Operation(summary = "Enable users")
    @PatchMapping("/")
    public Mono<WsResponse> enableUser(@RequestHeader Map<String, String> headers){
        return userService.enableUser(headers);
    }

    @Operation(summary = "Enable users")
    @PostMapping("/registration")
    public Mono<WsResponse> registration(@RequestHeader Map<String, String> headers, @RequestBody Mono<UserDto> payload){
        return userService.registration(headers, payload);
    }

    @Operation(summary = "Fetch user by code")
    @GetMapping("/find-by-code")
    public Mono<WsResponse> fetchByCode(@RequestHeader Map<String, String> headers){
        return userService.fetchByCode(headers);
    }

    @Operation(summary = "Fetch user by role")
    @GetMapping("/find-by-role/{pageNo}/{pageSize}")
    public Mono<WsResponse> fetchByRole(@RequestHeader Map<String, String> headers, @PathVariable(name = "pageNo") int pageNo, @PathVariable(name = "pageSize") int pageSize){
        return userService.fetchByRole(headers, pageNo, pageSize);
    }

    @Operation(summary = "Fetch Users paginated")
    @GetMapping("/{pageNo}/{pageSize}")
    public Mono<WsResponse> fetchAllPaginated(@RequestHeader Map<String, String> headers, @PathVariable(name = "pageNo") int pageNo, @PathVariable(name = "pageSize") int pageSize){
        return userService.fetchAllPaginated(headers, pageNo, pageSize);
    }

    @Operation(summary = "Fetch user by role and code")
    @PostMapping("/find-by-role-and-code")
    public Mono<WsResponse> fetchByRoleAndCode(@RequestHeader Map<String, String> headers, @RequestBody Mono<RoleAndCodeDto> payload){
        return userService.fetchByRoleAndCode(headers, payload);
    }

    @Operation(summary = "Fetch users who under by email")
    @GetMapping("/find-by-role-email")
    public Mono<WsResponse> fetchUserByEmailAndRoleHierarchy(@RequestHeader Map<String, String> headers){
        return userService.fetchUserByEmailAndRoleHierarchy(headers);
    }

    @Operation(summary = "Fetch user by status, role and code")
    @PostMapping("/find-by")
    public Mono<WsResponse> fetchByStatusAndRoleAndCode(@RequestHeader Map<String, String> headers, @RequestBody Mono<StatusAndRoleAndCodeDto> payload){
        return userService.fetchByStatusAndRoleAndCode(headers, payload);
    }

    @Operation(summary = "Remove code from user")
    @PatchMapping("/de-link-code-from-user")
    public Mono<WsResponse> deLinkCodeFromUser(@RequestHeader Map<String, String> headers){
        return userService.deLinkCodeFromUser(headers);
    }

    @Operation(summary = "Remove code from user")
    @GetMapping("/logout")
    public Mono<WsResponse> logout(@RequestHeader Map<String, String> headers){
        return userService.logout(headers);
    }

    @Operation(summary = "log login time")
    @PatchMapping("/login-time")
    public Mono<WsResponse> loginTime(@RequestHeader Map<String, String> headers){
        return userService.loginTime(headers);
    }

    @Operation(summary = "Fetch user by status, role and code")
    @GetMapping("/find-org-users")
    public Mono<WsResponse> fetchOrgUsers(@RequestHeader Map<String, String> headers){
        return userService.fetchOrgUsers(headers);
    }

    @Operation(summary = "Deactivate user")
    @GetMapping("/deactivate")
    public Mono<WsResponse> deactivateUser(@RequestHeader Map<String, String> headers){
        return userService.deactivateUser(headers);
    }

    @Operation(summary = "Update User Otp")
    @PutMapping("/")
    public Mono<WsResponse> updateUserOtp(@RequestHeader Map<String, String> headers, @RequestBody Mono<UserDto> payload){
        return userService.updateUserOtp(headers, payload);
    }
}
