package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.service.OutletTypeService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * REST controller for managing Outlet Types.
 * Provides reactive CRUD endpoints for outlet type resources.
 */
@RestController
@RequestMapping("/api/outlet-types")
@RequiredArgsConstructor
@Validated
public class OutletTypeController {
    private final OutletTypeService outletTypeService;

    /**
     * Create a new OutletType.
     * @param dto Mono of OutletTypeDto (validated)
     * @return Mono emitting ResponseEntity with OutletTypeResponse
     */
    @PostMapping("/")
    public Mono<WsResponse> create(@Valid @RequestBody final OutletTypeDto dto, @RequestHeader Map<String, String> headers) {
        return outletTypeService.create(dto, headers);
    }

    /**
     * Update an existing OutletType by id.
     * @param id OutletType id
     * @param dto Mono of OutletTypeDto (validated)
     * @return Mono emitting ResponseEntity with OutletTypeResponse
     */
    @PutMapping("/{id}")
    public Mono<WsResponse> update(@PathVariable final Long id,
                                                          @Valid @RequestBody final OutletTypeDto dto, @RequestHeader Map<String, String> headers) {
        return outletTypeService.update(id, dto, headers);
    }

    /**
     * Delete an OutletType by id.
     * @param id OutletType id
     * @return Mono emitting ResponseEntity<Void>
     */
    @DeleteMapping("/{id}")
    public Mono<WsResponse> delete(@PathVariable final Long id, @RequestHeader Map<String, String> headers) {
        return outletTypeService.delete(id, headers);
    }

    /**
     * Get an OutletType by id.
     * @param id OutletType id
     * @return Mono emitting ResponseEntity with OutletTypeResponse
     */
    @GetMapping("/{id}")
    public Mono<WsResponse> getById(@PathVariable final Long id, @RequestHeader Map<String, String> headers) {
        return outletTypeService.getById(id, headers);
    }

    /**
     * Get all OutletTypes.
     * @return Flux emitting OutletTypeResponse
     */
    @GetMapping("/")
    public Mono<WsResponse> getAll(@RequestHeader Map<String, String> headers) {
        return outletTypeService.getAll(headers);
    }

    /**
     * Get all OutletTypes by organizationTypeId.
     * @param organizationTypeId OrganizationType id
     * @return Flux emitting OutletTypeResponse
     */
    @GetMapping("/organization-type/{organizationTypeId}")
    public Mono<WsResponse> getByOrganizationTypeId(@PathVariable final Long organizationTypeId, @RequestHeader Map<String, String> headers) {
        return outletTypeService.getByOrganizationTypeId(organizationTypeId, headers);
    }
}
