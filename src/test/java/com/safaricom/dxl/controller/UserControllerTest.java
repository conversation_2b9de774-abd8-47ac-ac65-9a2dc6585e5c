package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.pojos.ErrorMapping;
import com.safaricom.dxl.data.pojos.IprsErrorMapping;
import com.safaricom.dxl.data.pojos.KycErrorMapping;
import com.safaricom.dxl.data.pojos.PaginationList;
import com.safaricom.dxl.data.repositories.*;
import com.safaricom.dxl.service.impl.UserServiceImpl;
import com.safaricom.dxl.utils.Iprs;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.utils.UserOnboard;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.Map;

import static com.safaricom.dxl.TestUtilities.*;
import static com.safaricom.dxl.data.enums.RegistrationStatus.IPRS_REG_FAILED;
import static com.safaricom.dxl.data.enums.RegistrationStatus.SUB_REG_MATCH;
import static com.safaricom.dxl.utils.MsStarterVariables.ERR_SUCCESS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class UserControllerTest {
    @Mock
    private WsResponseMapper responseMapper;
    @Mock
    private ClustersRepository clustersRepository;
    @Mock
    private RoleRepository rolesRepository;
    @Mock
    private UsersRepository usersRepository;
    @Mock
    private OrgRepository orgRepository;
    @Mock
    private Shared shared;
    @Mock
    private SSOToken ssoToken;
    @Mock
    private Iprs iprs;
    @Mock
    private UserRepositoryPort userRepositoryPort;
    @Mock
    private UserFailedRequestRepository userFailedRequestRepository;
    @Mock
    private UserOnboard userOnboard;
    @Mock
    private UserOnBoardLogsRepository userOnBoardLogsRepository;
    private UserController userController;
    Map<String, String> headers;
    Map<String, String> headers2;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.openMocks(this);
        userController = new UserController(new UserServiceImpl(responseMapper, clustersRepository, rolesRepository, usersRepository, orgRepository,
                shared, ssoToken, userRepositoryPort, userFailedRequestRepository, iprs, userOnboard, userOnBoardLogsRepository));
        headers = getHashMapHttpHeaders("1","1","1", "role");
        headers2 = getHashMapHttpHeadersNoEmail("ddddd");
    }

    @Test
    void addUserDtoValidation() {
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping("Kindly enter a valid role category.")));
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddUserTokenValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddUserRoleNameValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddUserSafEmailValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntity());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createRoleEntityB()));
        /* validateSafEmail */
        when(shared.validateSafEmail(anyString(), any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoNoneSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void addUserNullCodeValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntity());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createOrgRoleEntity()));
        /* validateSafEmail */
        when(shared.validateSafEmail(anyString(), any())).thenReturn(Mono.just(new ErrorMapping()));
        /* shared.validateCodeString */
        when(shared.validateCodeString(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(shared.validateCode(any())).thenReturn(new ErrorMapping("Error"));
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoNoneSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void addUserCodeExistsValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntity());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createOrgRoleEntity()));
        /* validateSafEmail */
        when(shared.validateSafEmail(anyString(), any())).thenReturn(Mono.just(new ErrorMapping()));
        /* shared.validateCodeString */
        when(shared.validateCodeString(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(shared.validateCode(any())).thenReturn(new ErrorMapping());
        when(orgRepository.findByCode(any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoNoneSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }


    @Test
    void AddUserIPRSFailedValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", IPRS_REG_FAILED)));
        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSFailMono());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddExistingUser(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", IPRS_REG_FAILED)));
        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSPassMono());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr400);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_BAD_REQUEST_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddExistingUser1(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(createUserAlreadyExists());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr400);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_BAD_REQUEST_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void validateUserCode(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", IPRS_REG_FAILED)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntityWithCodes());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createSpRoleEntity()));
        when(shared.validateSafEmail(anyString(), any())).thenReturn(errorMappingSuccess());
        when(shared.validateCodeString(any())).thenReturn(errorMappingSuccess());
        when(shared.validateCode(any())).thenReturn(new ErrorMapping());
        when(orgRepository.findByCode(any())).thenReturn(Mono.just(createOrgEntity()));
        when(userOnboard.validateDirectorKycApprovalOld(any(), any())).thenReturn(Mono.just(new KycErrorMapping("Error")));
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.add(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", IPRS_REG_FAILED)));
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntityNoCodes());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createSpRoleEntity()));
        when(shared.validateSafEmail(anyString(), any())).thenReturn(errorMappingSuccess());
        when(shared.validateCodeString(any())).thenReturn(errorMappingSuccess());
        when(shared.validateCode(any())).thenReturn(new ErrorMapping());
        when(orgRepository.findByCode(any())).thenReturn(Mono.just(createOrgEntity()));
        when(userOnboard.validateDirectorKycApprovalOld(any(), any())).thenReturn(Mono.just(new KycErrorMapping()));
        when(userRepositoryPort.updateUser(any(), any())).thenReturn(getLong());
        when(shared.returnUserDetails(any(), any(), anyString())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.add(headers, createUserDtoSaf2()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void AddUserIPRSPassValidation(){
        when(shared.validateUser(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(iprs.validate(any(), any(), anyBoolean())).thenReturn(Mono.just(new IprsErrorMapping("Test", SUB_REG_MATCH)));
        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSFailMono());
        when(userFailedRequestRepository.save(any())).thenReturn(createUserFailedRequestMono());
        when(usersRepository.delete(any())).thenReturn(Mono.empty());
        when(shared.encryptUser(any())).thenReturn(createUsersEntityWithCodes());
        when(rolesRepository.findByRoleName(anyString())).thenReturn(Mono.just(createOrgRoleEntity()));
        /* validateSafEmail */
        when(shared.validateSafEmail(anyString(), any())).thenReturn(Mono.just(new ErrorMapping()));
        /* shared.validateCodeString */
        when(shared.validateCodeString(any())).thenReturn(errorMappingFailure());
        when(usersRepository.save(any())).thenReturn(Mono.just(createUsersEntityWithCodes()));
        when(shared.returnUserDetails(anyMap(), any(), anyString())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.add(headers, createUserDtoNoneSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchUserByEmail() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchUserByEmail(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchUserByEmail(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntity()));
        when(shared.returnUserDetails(any(), any(), anyString())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchUserByEmail(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityTerritory()));
        when(clustersRepository.findByTerritory(any())).thenReturn(Flux.just(createClusterEntity()));
        StepVerifier.create(userController.fetchUserByEmail(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityRegion()));
        when(clustersRepository.findByRegion(any())).thenReturn(Flux.just(createClusterEntity()));
        StepVerifier.create(userController.fetchUserByEmail(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchAllPaginated() {
        when(shared.pagingValidation(10)).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(shared.pagingValidation(300)).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.fetchAllPaginated(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(usersRepository.findBy(any())).thenReturn(Flux.just(createUsersEntityEmpty(), createUsersEntityEmpty()));
        when(usersRepository.count()).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void enableUser() {
        when(shared.validateSession(anyMap())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), anyString())).thenReturn(wsResponseMonoErr401);
        StepVerifier.create(userController.enableUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_UNAUTHORIZED_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateSession(anyMap())).thenReturn(errorMappingSuccess());
        when(usersRepository.findByEmail(any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.enableUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityNoEnabled()));
        when(shared.customResponse(anyMap(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.enableUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityIPRSPassed()));
        when(userRepositoryPort.enableUser(any(), any())).thenReturn(getLong());
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.enableUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityEnabled()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.enableUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void deactivateUser() {
        when(shared.validateUserEmail(anyMap())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateUserEmail(anyMap())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), any())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityEnabled()));
        when(usersRepository.findByEmailAndRoleIn(anyString(), any())).thenReturn(Mono.just(createUsersEntityEnabled()));
        when(userRepositoryPort.disableUser(anyLong(), any())).thenReturn(getLong());
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(any())).thenReturn(Mono.just(createUsersEntityEnabled()));
        when(usersRepository.findByEmailAndRoleIn(anyString(), any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), any())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmailAndRoleIn(anyString(), any())).thenReturn(Mono.just(createUsersEntityNoEnabled()));
        when(shared.customResponse(anyMap(), any(), any(), anyString())).thenReturn(wsResponseMonoErr304);
        StepVerifier.create(userController.deactivateUser(headers))
                .consumeNextWith(resp-> assertEquals(304, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchByCode() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByCode(any())).thenReturn(Flux.empty());
        when(usersRepository.countByCode(any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByCode(any())).thenReturn(Flux.just(createUsersEntityNoCodes()));
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, createUserList())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchByRole() {
        when(shared.pagingValidation(anyInt())).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.fetchByRole(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.pagingValidation(anyInt())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchByRole(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByRole(any(), any())).thenReturn(Flux.just(createUsersEntityEmpty()));
        when(usersRepository.countByRole(anyString())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchByRole(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByRole(any(), any())).thenReturn(Flux.just(createUsersEntityRegion()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchByRole(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByRoleAndCode(){
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchByRoleAndCode(headers, createRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByRoleAndCodes(any(), any(), anyInt(), anyInt())).thenReturn(Flux.empty());
        when(usersRepository.countByRoleAndCodes(any(), any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchByRoleAndCode(headers, createRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByRoleAndCodes(any(), any(), anyInt(), anyInt())).thenReturn(Flux.just(createUsersEntity()));
        when(usersRepository.countByRoleAndCodes(any(), any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchByRoleAndCode(headers, createRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchByStatusAndRoleAndCode() {
        when(shared.validateRoleCodeDto(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.fetchByStatusAndRoleAndCode(headers2, createStatusAndRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateRoleCodeDto(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchByStatusAndRoleAndCode(headers2, createStatusAndRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByIsEnabledAndRoleAndCodes(anyBoolean(), any(), any(), anyInt(), anyInt())).thenReturn(Flux.just(createUsersEntityEmpty()));
        when(usersRepository.countByIsEnabledAndRoleAndCodes(anyBoolean(), any(), any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList(1L, new ArrayList<>())));
        when(responseMapper.setApiResponse((String) any(), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchByStatusAndRoleAndCode(headers2, createStatusAndRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByIsEnabledAndRoleAndCodes(anyBoolean(), any(), any(), anyInt(), anyInt())).thenReturn(Flux.just(createUsersEntityTerritory()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchByStatusAndRoleAndCode(headers, createStatusAndRoleAndCodeDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void logout(){
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.logout(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void registration(){
        when(shared.validateRegistration(any())).thenReturn(errorMappingFailure());
        when(userOnBoardLogsRepository.save(any())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.registration(headers2, createUserDtoIsUpdate()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateRegistration(any())).thenReturn(errorMappingSuccess());
        when(userOnboard.processRegistrationRequest(anyMap(), any())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.registration(headers2, createUserDtoIsUpdate()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void deLinkCodeFromUser(){

        when(ssoToken.validateForToken(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.deLinkCodeFromUser(headers2))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.deLinkCodeFromUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSPassMono());
        when(shared.processDeLinkCodeFromUser(anyMap(), any())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.deLinkCodeFromUser(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void loginTime(){
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.loginTime(headers2))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.loginTime(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSPassMono());
        when(userRepositoryPort.updateLoginTime(anyLong(), any())).thenReturn(getLong());
        when(shared.returnUserDetails(anyMap(), any(), anyString())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.loginTime(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchOrgUsers(){
        when(shared.validateCode(any())).thenReturn(new ErrorMapping("Error"));
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.fetchOrgUsers(headers))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateCode(any())).thenReturn(new ErrorMapping());
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchOrgUsers(headers2))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByCode(any())).thenReturn(Flux.empty());
        when(usersRepository.countByCode(any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchOrgUsers(headers2))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

    }

    @Test
    void fetchUserByEmailAndRoleHierarchy(){
        when(shared.validateRoleAndEmail(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.fetchUserByEmailAndRoleHierarchy(headers))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateRoleAndEmail(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.fetchUserByEmailAndRoleHierarchy(headers2))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.fetchUserByEmailAndRoleHierarchy(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSPassMono());
        when(usersRepository.findByRoleIn(any(), any())).thenReturn(Flux.empty());
        when(usersRepository.countByRoleIn(any())).thenReturn(getLong());
        when(shared.getUserListPaginated(any())).thenReturn(Mono.just(new PaginationList()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.fetchUserByEmailAndRoleHierarchy(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void updateUserOtp(){
        when(shared.validatePhone(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(userController.updateUserOtp(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validatePhone(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(any(), any(), anyString())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(userController.updateUserOtp(headers2, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(any())).thenReturn(tokenErrorMappingSuccess());
        when(usersRepository.findByEmail(anyString())).thenReturn(Mono.empty());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(userController.updateUserOtp(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(anyString())).thenReturn(createUserIPRSFailMono());
        when(usersRepository.findById(anyLong())).thenReturn(createUserIPRSFailMono());
        when(shared.customResponse(any(), any(), any(), anyString())).thenReturn(wsResponseMonoErr400);
        StepVerifier.create(userController.updateUserOtp(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_BAD_REQUEST_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(usersRepository.findByEmail(anyString())).thenReturn(createSuperUserMono());
        when(shared.processUpdateOtpRequest(anyMap(), any())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(userController.updateUserOtp(headers, createUserDtoSaf()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }
}