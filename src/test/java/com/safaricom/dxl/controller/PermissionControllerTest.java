package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.pojos.ErrorMapping;
import com.safaricom.dxl.data.repositories.PermissionRepository;
import com.safaricom.dxl.service.impl.PermissionServiceImpl;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static com.safaricom.dxl.TestUtilities.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class PermissionControllerTest {
    @Mock
    private WsResponseMapper responseMapper;
    @Mock
    private PermissionRepository repository;
    @Mock
    private Shared shared;
    @Mock
    private SSOToken ssoToken;
    private PermissionController permissionController;
    private PermissionServiceImpl permissionServiceImpl;
    Map<String, String> headers;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.openMocks(this);
        permissionServiceImpl =new PermissionServiceImpl( repository, shared, responseMapper, ssoToken);
        permissionController =new PermissionController(permissionServiceImpl);
        headers = getHashMapHttpHeaders("1","1","1", "role");
    }

    @Test
    void add() {
        when(shared.validatePermission(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(permissionController.add(headers, createNullPermissionDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validatePermission(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(permissionController.add(headers, createPermissionDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(repository.findByPermission(anyString())).thenReturn(Mono.empty());
        when(repository.save(any())).thenReturn(Mono.just(createPermissionEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(permissionController.add(headers, createPermissionDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(repository.findByPermission(anyString())).thenReturn(Mono.just(createPermissionEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(permissionController.add(headers, createPermissionDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchById() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        when(repository.findById(anyString())).thenReturn(Mono.empty());
        StepVerifier.create(permissionController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(repository.findById(anyString())).thenReturn(Mono.empty());
        StepVerifier.create(permissionController.fetchById(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(repository.findById((String) any())).thenReturn(Mono.just(createPermissionEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(permissionController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByPermission() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        when(repository.findByPermission(anyString())).thenReturn(Mono.empty());
        StepVerifier.create(permissionController.fetchByPermission(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(repository.findByPermission(anyString())).thenReturn(Mono.empty());
        StepVerifier.create(permissionController.fetchByPermission(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(repository.findByPermission(any())).thenReturn(Mono.just(createPermissionEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(permissionController.fetchByPermission(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchAllPaginated() {
        when(shared.pagingValidation(300)).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), anyString(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(permissionController.fetchAllPaginated(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(shared.pagingValidation(10)).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(permissionController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(repository.findBy(any())).thenReturn(Flux.just(createPermissionEntity(), createPermissionEntity()));
        when(repository.count()).thenReturn(getLong());
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(permissionController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }
}