package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.repositories.OrgBranchesRepository;
import com.safaricom.dxl.data.repositories.OrgRepository;
import com.safaricom.dxl.service.impl.OrgServiceImpl;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static com.safaricom.dxl.TestUtilities.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class OrgControllerTest {
    @Mock
    private WsResponseMapper responseMapper;
    @Mock
    private OrgBranchesRepository orgBranchesRepository;
    @Mock
    private OrgRepository orgRepository;
    @Mock
    private Shared shared;
    @Mock
    private SSOToken ssoToken;
    private OrgController orgController;
    private OrgServiceImpl orgServiceImpl;
    Map<String, String> headers;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.openMocks(this);
        orgServiceImpl =new OrgServiceImpl( orgRepository, orgBranchesRepository, responseMapper, shared, ssoToken);
        orgController =new OrgController(orgServiceImpl);
        headers = getHashMapHttpHeaders("1","1","1", "role");
    }

    @Test
    void add() {
        when(shared.validateOrgDetails(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgController.add(headers, createOrgDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateOrgDetails(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgController.add(headers, createOrgDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgRepository.findByCode(any())).thenReturn(Mono.empty());
        when(orgRepository.save(any())).thenReturn(Mono.just(createOrgEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.add(headers, createOrgNoBranchIdDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(orgBranchesRepository.findById(anyLong())).thenReturn(Mono.empty());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(orgController.add(headers, createOrgDto()))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(orgRepository.findByCode(any())).thenReturn(Mono.just(createOrgEntity()));
        when(orgBranchesRepository.findById(anyLong())).thenReturn(Mono.just(createOrgBranchEntity()));
        when(orgRepository.save(any())).thenReturn(Mono.just(createOrgEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.add(headers, createOrgDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchById() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgRepository.findById(anyLong())).thenReturn(Mono.empty());
        StepVerifier.create(orgController.fetchById(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(orgRepository.findById(anyLong())).thenReturn(Mono.just(createOrgEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByName() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgController.fetchByName(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgRepository.findByOrgName(any())).thenReturn(Flux.just(createOrgEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.fetchByName(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByCode() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgRepository.findByCode(any())).thenReturn(Mono.empty());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(orgController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(orgRepository.findByCode(any())).thenReturn(Mono.just(createOrgEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.fetchByCode(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchAllPaginated() {
        when(shared.pagingValidation(300)).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgController.fetchAllPaginated(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.pagingValidation(10)).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgRepository.findBy(any())).thenReturn(Flux.just(createOrgEntity(), createOrgEntity()));
        when(orgRepository.count()).thenReturn(getLong());
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchAllOrgType() {
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgController.fetchAllOrgType(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }
}