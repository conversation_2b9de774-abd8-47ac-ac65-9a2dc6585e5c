package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.repositories.OrgBranchesRepository;
import com.safaricom.dxl.data.repositories.OrgRepository;
import com.safaricom.dxl.service.impl.OrgBranchesServiceImpl;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static com.safaricom.dxl.TestUtilities.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class OrgBranchesControllerTest {

    @Mock
    private WsResponseMapper responseMapper;
    @Mock
    private OrgBranchesRepository orgBranchesRepository;
    @Mock
    private OrgRepository orgRepository;
    @Mock
    private Shared shared;
    @Mock
    private SSOToken ssoToken;
    private OrgBranchesController orgBranchesController;
    private OrgBranchesServiceImpl orgBranchesServiceImpl;
    Map<String, String> headers;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.openMocks(this);
        orgBranchesServiceImpl =new OrgBranchesServiceImpl(responseMapper, orgBranchesRepository, orgRepository, shared, ssoToken);
        orgBranchesController =new OrgBranchesController(orgBranchesServiceImpl);
        headers = getHashMapHttpHeaders("1","1","1", "role");
    }

    @Test
    void add() {
        when(shared.validateOrgBranchDetails(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgBranchesController.add(headers, createOrgBranchDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(shared.validateOrgBranchDetails(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgBranchesController.add(headers, createOrgBranchDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgBranchesRepository.findByBranchCode(any())).thenReturn(Mono.empty());
        when(orgRepository.findById(anyLong())).thenReturn(Mono.empty());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgBranchesController.add(headers, createOrgBranchDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(orgBranchesRepository.findByBranchCode(any())).thenReturn(Mono.empty());
        when(orgRepository.findById(anyLong())).thenReturn(Mono.empty());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgBranchesController.add(headers, createOrgBranchDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(orgBranchesRepository.findByBranchCode(any())).thenReturn(Mono.just(createOrgBranchEntity()));
        when(orgRepository.findById(anyLong())).thenReturn(Mono.just(createOrgEntity()));
        when(orgBranchesRepository.save(any())).thenReturn(Mono.just(createOrgBranchEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgBranchesController.add(headers, createOrgBranchDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchById() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgBranchesController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgBranchesRepository.findById(anyLong())).thenReturn(Mono.empty());
        StepVerifier.create(orgBranchesController.fetchById(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(orgBranchesRepository.findById(anyLong())).thenReturn(Mono.just(createOrgBranchEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgBranchesController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchByBranchName() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgBranchesController.fetchByBranchName(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgBranchesRepository.findByBranchName(any())).thenReturn(Mono.empty());
        StepVerifier.create(orgBranchesController.fetchByBranchName(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(orgBranchesRepository.findByBranchName(any())).thenReturn(Mono.just(createOrgBranchEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgBranchesController.fetchByBranchName(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByOrgId() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgBranchesController.fetchByOrgId(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess()); when(orgBranchesRepository.findByOrgId(anyString())).thenReturn(Flux.just(createOrgBranchEntity(), createOrgBranchEntity()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgBranchesController.fetchByOrgId(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchAllPaginated() {
        when(shared.pagingValidation(300)).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(orgBranchesController.fetchAllPaginated(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.pagingValidation(10)).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(orgBranchesController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(orgBranchesRepository.findBy(any())).thenReturn(Flux.just(createOrgBranchEntity(), createOrgBranchEntity()));
        when(orgBranchesRepository.count()).thenReturn(getLong());
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(orgBranchesController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }
}