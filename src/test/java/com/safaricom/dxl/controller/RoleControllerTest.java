package com.safaricom.dxl.controller;

import com.safaricom.dxl.config.MsConfigProperties;
import com.safaricom.dxl.config.MsRedisConfig;
import com.safaricom.dxl.data.pojos.ErrorMapping;
import com.safaricom.dxl.data.repositories.PermissionRepository;
import com.safaricom.dxl.data.repositories.RoleRepository;
import com.safaricom.dxl.service.impl.RoleServiceImpl;
import com.safaricom.dxl.utils.SSOToken;
import com.safaricom.dxl.utils.Shared;
import com.safaricom.dxl.webflux.starter.exception.WsResourceNotFoundException;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static com.safaricom.dxl.TestUtilities.*;
import static com.safaricom.dxl.utils.MsStarterVariables.ERR_SUCCESS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class RoleControllerTest {
    @Mock
    private WsResponseMapper responseMapper;
    @Mock
    private PermissionRepository permissionRepository;
    @Mock
    private RoleRepository roleRepository;
    @Mock
    private Shared shared;
    @Mock
    private SSOToken ssoToken;
    @Mock
    private MsRedisConfig.RateLimiter rateLimiter;
    @Mock
    private MsConfigProperties properties;
    private RoleController roleController;
    private RoleServiceImpl roleServiceImpl;
    Map<String, String> headers;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.openMocks(this);
        roleServiceImpl =new RoleServiceImpl(responseMapper, roleRepository, permissionRepository, shared, ssoToken);
        roleController =new RoleController(roleServiceImpl, rateLimiter, properties);
        headers = getHashMapHttpHeaders("1","1","1", "role");
    }

    @Test
    void add() {
        when(shared.validateRoleCreation(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(roleController.add(headers, createRoleDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateRoleCreation(any())).thenReturn(Mono.just(new ErrorMapping()));
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.add(headers, createRoleDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findByCategoryAndRoleName(any(), any())).thenReturn(Mono.empty());
        when(permissionRepository.findAllById(anySet())).thenReturn(Flux.empty());
        when(shared.customResponse(anyMap(), anyString(), anyString(), anyString())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(roleController.add(headers, createRoleDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(roleRepository.findByCategoryAndRoleName(any(), any())).thenReturn(Mono.just(createRoleEntityB()));
        when(permissionRepository.findAllById(anySet())).thenReturn(Flux.just(createPermissionEntity()));
        when(roleRepository.save(any())).thenReturn(Mono.just(createRoleEntityA()));
        when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.add(headers, createRoleDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchById() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findById(anyLong())).thenReturn(Mono.empty());
        StepVerifier.create(roleController.fetchById(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(roleRepository.findById(anyLong())).thenReturn(Mono.just(createSpRoleEntity()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.fetchById(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }

    @Test
    void fetchByRole() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.fetchByRole(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findByRoleName(anyString())).thenReturn(Mono.empty());
        StepVerifier.create(roleController.fetchByRole(headers))
                .expectError(WsResourceNotFoundException.class)
                .verify();

        when(roleRepository.findByRoleName(any())).thenReturn(Mono.just(createSpRoleEntity()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.fetchByRole(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchByCategory() {
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.fetchByCategory(headers))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findByCategory(any())).thenReturn(Flux.just(createSpRoleEntity()));
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.fetchByCategory(headers))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void fetchAllPaginated() {
        when(shared.pagingValidation(300)).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(roleController.fetchAllPaginated(headers, 1, 300))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.pagingValidation(10)).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findBy(any())).thenReturn(Flux.just(createSpRoleEntity(), createSpRoleEntity()));
        when(roleRepository.count()).thenReturn(getLong());
        when(responseMapper.setApiResponse(eq(ERR_SUCCESS), (Object) any(), any(), anyBoolean(), anyMap())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.fetchAllPaginated(headers, 1, 10))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();
    }

    @Test
    void addOrRemovePermission() {
        when(shared.validateRoleDto(any())).thenReturn(errorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr465);
        StepVerifier.create(roleController.addOrRemovePermission(headers, createRoleUpdateDto()))
                .consumeNextWith(resp-> assertEquals(ERR_VALIDATION_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(shared.validateRoleDto(any())).thenReturn(errorMappingSuccess());
        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingFailure());
        when(shared.customResponse(anyMap(), any(), any())).thenReturn(wsResponseMonoErr403);
        StepVerifier.create(roleController.addOrRemovePermission(headers, createRoleUpdateDto()))
                .consumeNextWith(resp-> assertEquals(ERR_FORBIDDEN_INT, resp.getHeader().getResponseCode())).verifyComplete();

        when(ssoToken.validation(anyMap())).thenReturn(tokenErrorMappingSuccess());
        when(roleRepository.findById(anyLong())).thenReturn(Mono.just(createSpRoleEntity()));
        when(shared.validatePermission(anyMap(), any(), any())).thenReturn(wsResponseMonoSucc);
        StepVerifier.create(roleController.addOrRemovePermission(headers, createRoleUpdateDto()))
                .consumeNextWith(resp-> assertEquals(ERR_SUCCESS_INT, resp.getHeader().getResponseCode()))
                .verifyComplete();

        when(roleRepository.findById(anyLong())).thenReturn(Mono.empty());
        when(shared.customResponse(anyMap(), any(), any(), any())).thenReturn(wsResponseMonoErr404);
        StepVerifier.create(roleController.addOrRemovePermission(headers, createRoleUpdateDto()))
                .consumeNextWith(resp-> assertEquals(ERR_NOT_FOUND_INT, resp.getHeader().getResponseCode())).verifyComplete();
    }
}