package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OrganizationTypeDto;
import com.safaricom.dxl.data.entities.OrganizationTypeEntity;
import com.safaricom.dxl.data.repositories.OrganizationTypeRepository;
import com.safaricom.dxl.data.repositories.OutletTypeRepository;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@DisplayName("OrganizationTypeServiceImpl Unit Tests")
class OrganizationTypeServiceImplTest {
    @Test
    @DisplayName("create_RepositoryThrowsNonChainedException_ErrorHandled")
    void create_RepositoryThrowsNonChainedException_ErrorHandled() {
        OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
        // Simulate error in findByName (outermost onErrorResume)
        when(organizationTypeRepository.findByName(anyString())).thenReturn(Mono.error(new RuntimeException("outer error")));

        StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
    }
    @Mock
    private OrganizationTypeRepository organizationTypeRepository;
    @Mock
    private OutletTypeRepository outletTypeRepository;
    @Mock
    private WsResponseMapper wsResponseMapper;
    @InjectMocks
    private OrganizationTypeServiceImpl service;

    private final Map<String, String> headers = Collections.singletonMap("key", "value");

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        service = new OrganizationTypeServiceImpl(organizationTypeRepository, outletTypeRepository, wsResponseMapper);
        // Prevent NPE in service.create() by stubbing findByName
        when(organizationTypeRepository.findByName(anyString())).thenReturn(Mono.empty());
    }

    @Test
    @DisplayName("create_DuplicateName_ThrowsDuplicateRecordException")
    void create_DuplicateName_ThrowsDuplicateRecordException() {
        OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
        OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
        when(organizationTypeRepository.findByName(anyString())).thenReturn(Mono.just(existing));

        StepVerifier.create(service.create(dto, headers))
                .expectError(com.safaricom.dxl.exception.DuplicateRecordException.class)
                .verify();
    }

    @Nested
    @DisplayName("create")
    class Create {
        @Test
        @DisplayName("create_RepositoryThrowsNotFoundException_ErrorPropagated")
        void create_RepositoryThrowsNotFoundException_ErrorPropagated() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.create(dto, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("create_ValidDto_ReturnsWsResponse")
        void create_ValidDto_ReturnsWsResponse() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity saved = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), null, null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(saved));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.create(dto, headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("create_RepositoryThrows_ErrorHandled")
        void create_RepositoryThrows_ErrorHandled() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.create(dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("update")
    class Update {
        @Test
        @DisplayName("update_RepositoryThrowsNonChainedException_ErrorHandled")
        void update_RepositoryThrowsNonChainedException_ErrorHandled() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            // Simulate error in findById (outermost onErrorResume)
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.error(new RuntimeException("outer error")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
        @Test
        @DisplayName("update_RepositoryThrowsNotFoundException_ErrorPropagated")
        void update_RepositoryThrowsNotFoundException_ErrorPropagated() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("update_ExistingId_ReturnsWsResponse")
        void update_ExistingId_ReturnsWsResponse() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            OrganizationTypeEntity updated = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), LocalDateTime.now(), null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(updated));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("update_NotFound_ThrowsNotFoundException")
        void update_NotFound_ThrowsNotFoundException() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.empty());

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("update_RepositoryThrows_ErrorHandled")
        void update_RepositoryThrows_ErrorHandled() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("update_RepositoryThrowsOtherException_ErrorMappedToInternalServerError")
        void update_RepositoryThrowsOtherException_ErrorMappedToInternalServerError() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.error(new IllegalArgumentException("illegal arg")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("delete")
    class Delete {
        @Test
        @DisplayName("delete_RepositoryThrowsChainedExceptionFalse_ErrorMappedToInternalServerError")
        void delete_RepositoryThrowsChainedExceptionFalse_ErrorMappedToInternalServerError() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            RuntimeException testException = new RuntimeException("test error");
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.error(testException));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                // Mock returnChainedException to return false for any throwable
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.delete(1L, headers))
                        .expectErrorSatisfies(throwable -> {
                            org.assertj.core.api.Assertions.assertThat(throwable)
                                    .isInstanceOf(InternalServerErrorException.class);
                            // The error message will be "test error" from the inner onErrorResume
                            // but if it reaches the outer onErrorResume, it might be different
                            // Let's just verify it's an InternalServerErrorException
                        })
                        .verify();
            }
        }
        @Test
        @DisplayName("delete_RepositoryThrowsNonChainedException_ErrorHandled")
        void delete_RepositoryThrowsNonChainedException_ErrorHandled() {
            // Simulate error in findById (outermost onErrorResume)
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.error(new RuntimeException("outer error")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
        @Test
        @DisplayName("delete_RepositoryThrowsNotFoundException_ErrorHandled")
        void delete_RepositoryThrowsNotFoundException_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_RepositoryThrowsInternalServerErrorException_ErrorHandled")
        void delete_RepositoryThrowsInternalServerErrorException_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
           when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.error(new InternalServerErrorException("internal error", "500")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_RepositoryThrowsNullPointerException_ErrorHandled")
        void delete_RepositoryThrowsNullPointerException_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
           when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.error(new NullPointerException("null error")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
        @Test
        @DisplayName("delete_ExistingId_ReturnsWsResponse")
        void delete_ExistingId_ReturnsWsResponse() {
            WsResponse wsResponse = mock(WsResponse.class);
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.delete(1L, headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("delete_RepositoryThrows_ErrorHandled")
        void delete_RepositoryThrows_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
           when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_WithNonEmptyOutlets_ThrowsForbiddenException")
        void delete_WithNonEmptyOutlets_ThrowsForbiddenException() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.just(mock(com.safaricom.dxl.data.entities.OutletTypeEntity.class)));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(com.safaricom.dxl.exception.ForbiddenException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("getById")
    class GetById {
        @Test
        @DisplayName("getById_ExistingId_ReturnsWsResponse")
        void getById_ExistingId_ReturnsWsResponse() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.getById(1L, headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("getById_NotFound_ThrowsNotFoundException")
        void getById_NotFound_ThrowsNotFoundException() {
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.empty());

            StepVerifier.create(service.getById(1L, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("getById_RepositoryThrows_ErrorHandled")
        void getById_RepositoryThrows_ErrorHandled() {
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.getById(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("getAll")
    class GetAll {
        @Test
        @DisplayName("getAll_OrganizationTypesExist_ReturnsWsResponse")
        void getAll_OrganizationTypesExist_ReturnsWsResponse() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.getAll(headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("getAll_NoneExist_ThrowsNotFoundException")
        void getAll_NoneExist_ThrowsNotFoundException() {
            when(organizationTypeRepository.findAll()).thenReturn(Flux.empty());

            StepVerifier.create(service.getAll(headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAll_RepositoryThrows_ErrorHandled")
        void getAll_RepositoryThrows_ErrorHandled() {
            when(organizationTypeRepository.findAll()).thenReturn(Flux.error(new RuntimeException("db error")));

            StepVerifier.create(service.getAll(headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("getAllWithOutlets")
    class GetAllWithOutlets {
        @Test
        @DisplayName("getAllWithOutlets_RepositoryThrowsNonChainedException_ErrorHandled")
        void getAllWithOutlets_RepositoryThrowsNonChainedException_ErrorHandled() {
            // Simulate error in findAll (outermost onErrorResume)
            when(organizationTypeRepository.findAll()).thenReturn(Flux.error(new RuntimeException("outer error")));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }
        @Test
        @DisplayName("getAllWithOutlets_OrganizationTypesExist_ReturnsWsResponse")
        void getAllWithOutlets_OrganizationTypesExist_ReturnsWsResponse() {
            OrganizationTypeEntity orgEntity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(orgEntity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.just(
                com.safaricom.dxl.data.entities.OutletTypeEntity.builder()
                    .id(1L)
                    .dealerName("Outlet")
                    .description("desc")
                    .organizationTypeId(1L)
                    .created(LocalDateTime.now())
                    .build()
            ));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("getAllWithOutlets_NoneExist_ThrowsNotFoundException")
        void getAllWithOutlets_NoneExist_ThrowsNotFoundException() {
            when(organizationTypeRepository.findAll()).thenReturn(Flux.empty());

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAllWithOutlets_OutletTypeRepositoryThrows_ErrorHandled")
        void getAllWithOutlets_OutletTypeRepositoryThrows_ErrorHandled() {
            OrganizationTypeEntity orgEntity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(orgEntity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.error(new RuntimeException("db error")));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAllWithOutlets_OrganizationTypesExistWithEmptyOutlets_ReturnsWsResponse")
        void getAllWithOutlets_OrganizationTypesExistWithEmptyOutlets_ReturnsWsResponse() {
            OrganizationTypeEntity orgEntity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            WsResponse wsResponse = mock(WsResponse.class);

            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(orgEntity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectNext(wsResponse)
                    .verifyComplete();
        }

        @Test
        @DisplayName("getAllWithOutlets_ResponseMapperThrows_ErrorHandled")
        void getAllWithOutlets_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity orgEntity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(orgEntity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAllWithOutlets_ResponseMapperThrowsChainedException_ErrorPropagated")
        void getAllWithOutlets_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeEntity orgEntity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(orgEntity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.getAllWithOutlets(headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }
    }

    @Nested
    @DisplayName("Additional Coverage Tests")
    class AdditionalCoverageTests {

        @Test
        @DisplayName("create_ResponseMapperThrows_ErrorHandled")
        void create_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity saved = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(saved));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.create(dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("update_ResponseMapperThrows_ErrorHandled")
        void update_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            OrganizationTypeEntity updated = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), LocalDateTime.now(), null, null);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(updated));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("getById_ResponseMapperThrows_ErrorHandled")
        void getById_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.getById(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAll_ResponseMapperThrows_ErrorHandled")
        void getAll_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.getAll(headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_NotFound_ThrowsNotFoundException")
        void delete_NotFound_ThrowsNotFoundException() {
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.empty());

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("create_ResponseMapperThrowsChainedException_ErrorPropagated")
        void create_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity saved = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(saved));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.create(dto, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("update_ResponseMapperThrowsChainedException_ErrorPropagated")
        void update_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeDto dto = OrganizationTypeDto.builder().name("TestOrg").description("desc").build();
            OrganizationTypeEntity existing = new OrganizationTypeEntity(1L, "Old", "old", LocalDateTime.now(), null, null, null);
            OrganizationTypeEntity updated = new OrganizationTypeEntity(1L, dto.getName(), dto.getDescription(), LocalDateTime.now(), LocalDateTime.now(), null, null);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(existing));
            when(organizationTypeRepository.save(any(OrganizationTypeEntity.class))).thenReturn(Mono.just(updated));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("getById_ResponseMapperThrowsChainedException_ErrorPropagated")
        void getById_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.getById(1L, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("getAll_ResponseMapperThrowsChainedException_ErrorPropagated")
        void getAll_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);

            when(organizationTypeRepository.findAll()).thenReturn(Flux.just(entity));
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.getAll(headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_ResponseMapperThrows_ErrorHandled")
        void delete_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
        }

        @Test
        @DisplayName("delete_ResponseMapperThrowsChainedException_ErrorPropagated")
        void delete_ResponseMapperThrowsChainedException_ErrorPropagated() {
            OrganizationTypeEntity entity = new OrganizationTypeEntity(1L, "TestOrg", "desc", LocalDateTime.now(), null, null, null);
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(entity));
            when(outletTypeRepository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            when(organizationTypeRepository.deleteById(1L)).thenReturn(Mono.empty());
            when(wsResponseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("not found", "404")));

            StepVerifier.create(service.delete(1L, headers))
                    .expectError(NotFoundException.class)
                    .verify();
        }
    }
}
