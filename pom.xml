<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.safaricom.dxl</groupId>
        <artifactId>dxl-webflux-starter-parent</artifactId>
        <version>1.4.6</version>
        <relativePath />
    </parent>

    <groupId>com.safaricom.dxl</groupId>
    <artifactId>ms-channels-partner-hub-reg</artifactId>
    <version>1.0.0</version>
	<packaging>jar</packaging>
	
    <name>ms-channels-partner-hub-reg</name>
    <description>DxL reactive microservice starter</description>
    <properties>
        <dxl-webflux.version>2.4.0</dxl-webflux.version>
        <sonar.exclusions>
            pom.xml,
            **/config/**/*,
            **/data/**/*,
            **/exception/*.java,
            **/utils/**/*,
            **/MsStarterApplication.java
        </sonar.exclusions>
        <sonar.coverage.exclusions>
            pom.xml,
            **/config/**/*,
            **/data/**/*,
            **/exception/*.java,
            **/utils/**/*,
            **/MsStarterApplication.java
        </sonar.coverage.exclusions>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.safaricom.dxl</groupId>
            <artifactId>encryption</artifactId>
            <version>3.0.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>r2dbc-postgresql</artifactId>
            <version>1.0.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.7</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/de.huxhorn.sulky/de.huxhorn.sulky.ulid -->
        <dependency>
            <groupId>de.huxhorn.sulky</groupId>
            <artifactId>de.huxhorn.sulky.ulid</artifactId>
            <version>8.3.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.kafka/kafka-clients -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>3.9.1</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-data-redis-reactive -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
            <version>3.5.0</version>
        </dependency>

        <!-- Netty Dependencies -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http2</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-handler</artifactId>
            <version>4.1.118.Final</version>
        </dependency>
        <dependency>
            <groupId>io.projectreactor.netty</groupId>
            <artifactId>reactor-netty-core</artifactId>
            <version>1.1.25</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>dxl-releases</id>
            <name>safaricom-dxl-releases</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-releases/</url>
        </repository>
        <repository>
            <id>dxl-snapshots</id>
            <name>safaricom-dxl-snapshot</name>
            <url>https://jfrog.safaricom.co.ke/artifactory/dxl-snapshot/</url>
        </repository>
    </repositories>
</project>
