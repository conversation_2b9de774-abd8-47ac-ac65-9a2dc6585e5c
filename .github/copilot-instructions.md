* ***IMPORTANT:***
**TASK MANAGEMENT:** FOR ALL USER REQUESTS, ALWAYS CREATE A STRUCTURED TASK LIST TO TRACK PROGRESS. EACH TASK SHOULD REPRESENT OBJECTIVE TO BE ACCOMPLISHED

**Core Requirements:**
1.  **Reactive First (Spring WebFlux):**
    * Exclusively use `Mono<T>` and `Flux<T>`. **NO BLOCKING OPERATIONS** (`block()`, `blockFirst()`, `Thread.sleep()`, blocking I/O).
    * Chain reactive operators effectively (e.g., `map`, `flatMap`, `filter`, `switchIfEmpty`, `onErrorResume`).
    * Implement robust reactive error handling (e.g., `onErrorMap`, `onErrorResume`). Signal errors appropriately.
    * Use `subscribeOn`/`publishOn` correctly if non-default scheduling is needed.
2.  **Company & Architectural Standards:**
    * **SOLID, DRY, Composition over Inheritance.**
    * **Immutability:** DTOs (prefer Java `record` types) and value objects must be immutable (`final` fields).
    * **Constructor Injection:** Exclusively use constructor-based dependency injection.
    * **Separation of Concerns:** Maintain clear distinctions between Controllers, Services, and Repositories.
3.  **Code Quality & Conventions:**
    * **Java 17+:** Utilize modern features appropriately. Use `final` for variables/parameters by default.
    * **Logging:** Standardized SLF4J logging for key events, parameters, and errors.
    * **Validation:** Use `@Validated`/`@Valid` for DTOs. Validate within reactive streams where appropriate.
    * **Clarity:** Write self-explanatory code. Maximize readability. Keep methods concise and single-purpose.
    * **Naming:** Follow conventions from our shared library (#sharedLibraryDocs).
4.  **Components Specifics (Reactive):**
    * **Controllers (`@RestController`):** Return `Mono<ResponseEntity<T>>` or `Flux<T>`. Accept `Mono<RequestBodyDto>`.
    * **Services (`@Service`):** Implement business logic with `Mono`/`Flux`.
    * **Repositories:** Use Spring Data R2DBC or reactive alternatives.
    * **DTOs:** Java `record`s preferred.
5.  **Documentation:**
    * Provide clear JavaDoc for all public methods and classes, detailing parameters, return types (especially what `Mono`/`Flux` emit), and exceptions.

**Key Anti-Patterns to AVOID:**
* **BLOCKING CALLS IN REACTIVE CODE.**
* Ignoring reactive error signals or improper error handling.
* Mixing blocking and non-blocking code without clear strategies (e.g., dedicated schedulers).
* Static state that impacts concurrency or testability.

**Verification Mindset:** Before outputting, mentally check if the generated code is:
* Correctly reactive and non-blocking?
* Robustly error-handled?
* Input-validated?
* Properly logged and documented?
* Adherent to core company standards (immutability, injection, naming)?

Provide clarifications if the request is ambiguous. Prioritize generating code that is maintainable and testable.

**General Test Guidelines:**

* **Requirements:**
1. **Sequential Processing:** Complete one class at a time. Do not move to the next class until the current one achieves 100% condition coverage.
2. **Task Management:** Create a structured task list to track progress through each class. Each task should represent completing 100% condition coverage for one specific class.
3. **Verification:** After writing tests for each class, verify that all conditions are covered before proceeding to the next class.
4. **Completion:** Once all classes achieve 100% condition coverage and all tests pass
* ***Below are some approach to consider in checking code coverage***
1. Use Run Tests to ensure all tests pass and no failing tests. If there is a failing test, FIX it FIRST.
2. Run mvn clean test jacoco:report
- Objective: Check condition coverage
- Operation: command: `mvn jacoco:report`

- Objective: Search jacoco csv file
- Operation: Search Tool: `jacoco.csv target/site/jacoco`

- Objective: Read jacoco report file
- Operation: Read Tool: `jacoco target/site

* **Focus & Simplicity:** Test **one method** or **one specific behavior** per test case. Keep tests clean, simple, and easy to understand.
* **Test Naming Convention:** `methodName_StateUnderTest_ExpectedResult`.
    * *Example:* `getUserById_UserExists_ReturnsUserDto`
    * *Example:* `processOrder_InvalidOrderData_ThrowsValidationException`
* **Structure (AAA Pattern / Given-When-Then):**
    * Clearly follow the **Arrange, Act, Assert** (AAA) pattern.
    * For BDD clarity, phrase your tests or use comments to reflect a **given-when-then** style.
        * `// Given: [Setup conditions]`
        * `// When: [Action is performed]`
        * `// Then: [Assertions are made]`
* **F.I.R.S.T. Principles:** Ensure tests are:
    * **Fast:** Run quickly.
    * **Independent/Isolated:** Tests should not depend on each other or external state that can change.
    * **Repeatable:** Produce the same results every time they are run.
    * **Self-Validating:** The test itself determines if it passed or failed, without manual interpretation.
    * **Timely (or Thorough):** Written at the right time (with the production code) and cover all important aspects.
* **Test Scenarios:** Cover both **positive (happy path)** scenarios and **negative scenarios** (error handling, invalid inputs, edge cases).
* **Setup with `@BeforeEach`:** Use `@BeforeEach` to initialize common objects, mock setups, or test data required by multiple test methods within the same test class. This avoids logic duplication within test cases.
* **Test Class Organization:**
    * **DisplayNames** Use `@DisplayName` with clear name depicting the purpose of the test
    * **Mirroring:** Test class names should mirror the class under test (e.g., `UserServiceTest` for `UserService`).
    * **`@Nested` Classes:** Use `@Nested` inner classes to group related tests for a specific method or a set of behaviors, enhancing readability and organization, especially for larger test classes.
    * **Similar Tests** Always use `@ParameterizedTest` for test cases that share common test parameters.
* **Assertions:**
    * Use JUnit 5 assertions (`org.junit.jupiter.api.Assertions.*`).
    * (Clarify if AssertJ is preferred/used: If so, `org.assertj.core.api.Assertions.*` should be used for fluent assertions).
    * **Crucially, test cases must always include at least one assertion to be considered valid by SonarQube quality gates.**
* **Mockito Usage:**
    * Mock external dependencies of the unit under test using `Mockito.mock([ClassToMock].class)` or the `@Mock` annotation.
    * Setup mock behavior: `when(...).thenReturn(...)`, `when(...).thenThrow(...)`, `doNothing().when(...)`, etc.
    * Verify interactions: `verify([mockObject], times(n)).[methodCall(arguments)]`.
    * **Matcher Usage (Important for SonarQube):**
        * Mockito requires that if one argument uses a matcher, all arguments must use matchers.
        * However, SonarQube flags unnecessary `eq()` invocations.
        * **Preference:** Avoid unnecessary `eq()` by passing literal values directly when no other matchers are needed for that specific mock interaction. If one argument *must* use a matcher (e.g., `any()`, `argThat()`), then use `eq()` or other appropriate matchers for all other literal value arguments in that same mocked method call. Strive for clarity and Sonar compliance.
        * *Example (Preferred if no other matchers needed):* `when(mockService.processData("specificValue", 10)).thenReturn(true);`
        * *Example (If a matcher is needed for one arg):* `when(mockService.processData(anyString(), eq(10))).thenReturn(true);`

**Specific Test Types:**

* **Service Unit Tests:**
    * Focus on business logic. Mock repositories and other services.
    * Test happy paths, error handling, and edge cases thoroughly.
* **Controller Unit Tests (`MockMvc` / `@WebMvcTest`):**
    * Focus on request mapping, request/response serialization, input validation.
    * Use `@WebMvcTest([ControllerClass].class)` and `@MockBean` for service dependencies.
    * Use `MockMvc` for performing requests and asserting responses.
* **Reactive Code Testing (e.g., Project Reactor):**
    * For methods returning `Mono` or `Flux`, use `StepVerifier` from `reactor.test` to test reactive sequences.
    * Verify emitted values, completion signals, and error signals.
    * *Example:*
        ```java
        StepVerifier.create(service.getReactiveData(id))
            .expectNextMatches(data -> data.isValid())
            .expectComplete()
            .verify();
        ```
    * Ensure tests for reactive code cover behavior under different conditions (e.g., empty flux, error mono).
* **Repository Tests (`@DataJpaTest`):**
    * For custom queries. Use `@DataJpaTest` with an in-memory DB or Testcontainers.
* ***IMPORTANT:***
1. DO NOT ASK FOR USER CONSENT TO PROCEED - WORK THROUGH ALL TASKS SYSTEMATICALLY UNTIL COMPLETION.
2. DURING TASKS ON UNIT TESTS, DO NOT ATTEMPT TO MAKE CHANGES TO THE IMPLEMENTATION CLASSESS WITHOUT USER'S CONSENT
3. USE INTERNAL TOOLS TO ACHIEVE YOUR TASKS
4. AFTER COMPLETING UIT TEST OF ONE CLASS, RUN TESTS TO ENSURE THEY PASS AND FIX ANY FAILING TESTS BEFORE PROCEEDING

**Workflow & Coverage Mandate (STEPS FOR ACHIEVING UNIT TESTS):**

Your primary goal is to help achieve comprehensive test coverage. Follow this iterative process, focusing on one class at a time:

1.  **Identify Target Classes:** Use Sonar exclusion configurations in the `pom.xml` (accessible via #sonarQubeAPI or by inspecting the file) to determine which files/classes *need* to be tested (i.e., are not excluded).
2.  **Assess Condition Coverage (JaCoCo):** For each class targeted for testing, assume you will use JaCoCo (via #jacocoReporter or similar tool) to check **condition coverage** after tests are written.
3.  **Ensure Thorough Conditional Coverage:**
    * If the condition coverage for a method/class is less than **90-95%** (aim for 100% but 95% is minimum acceptable), generate additional test cases to cover the **uncovered conditions**. This includes edge cases, boundary conditions, and different logical paths within conditional statements (if-else, switch, loops).
4.  **Verify and Iterate per Class:**
    * Once you've generated/updated tests to cover previously uncovered conditions for a **single class**, ensure all tests for that class pass.
    * Run coverage analysis for that class again to confirm the improvement.
5.  **Sequential Class Completion:** **NEVER proceed to generate tests for the next class until you have ensured that condition coverage targets are met and all tests for the current class pass.** The user prefers to complete and verify unit tests for one service (and its related classes) before moving to another service.

**Output Requirements:**
* When asked to generate tests for a class or method:
    1.  Identify dependencies to be mocked.
    2.  Determine common positive, negative, and edge case scenarios based on the code and the above guidelines.
    3.  If the code involves reactive types, use `StepVerifier`.
    4.  If generating tests for a class for the first time, or if coverage is low, explicitly state that you are aiming to cover various conditions and ask if there are specific complex conditions the user wants to highlight for that class.
    5.  Generate JUnit 5 test code with Mockito.


# Project Structure Guidelines

This document defines the standard structure for Java Spring Boot projects. Follow this template to ensure consistency, maintainability, and scalability across all projects.

## Directory Structure

<project-root>/ ├── Dockerfile ├── Jenkinsfile ├── pom.xml ├── README.md ├── src/ │ └── main/ │ ├── java/ │ │ └── <base_package>/ │ │ ├── <ApplicationMainClass>.java │ │ ├── component/ │ │ │ ├── <ComponentClass1>.java │ │ │ └── <ComponentClass2>.java │ │ ├── config/ │ │ │ └── <ConfigClass>.java │ │ ├── controller/ │ │ │ ├── <ControllerClass1>.java │ │ │ └── <ControllerClass2>.java │ │ ├── exception/ │ │ │ ├── <ExceptionClass1>.java │ │ │ └── <ExceptionClass2>.java │ │ ├── model/ │ │ │ ├── dto/ │ │ │ ├── enums/ │ │ │ └── pojo/ │ │ ├── repository/ │ │ │ ├── dao/ │ │ │ ├── entities/ │ │ │ └── repo/ │ │ ├── service/ │ │ │ ├── <ServiceClass1>.java │ │ │ ├── <ServiceClass2>.java │ │ │ ├── dispatcher/ │ │ │ └── integration/ │ │ └── utils/ │ │ ├── <UtilityClass1>.java │ │ └── <UtilityClass2>.java │ └── resources/ │ ├── application.properties │ └── logback.xml


## Guidelines

- Use `<base_package>` to represent your organization or project package (e.g., `com.safaricom.dxl`).
- Place the main Spring Boot application class in the root of the base package.
- Organize code into logical sub-packages:
  - `component/`: Reusable components and helpers.
  - `config/`: Configuration classes.
  - `controller/`: REST or Web controllers.
  - `exception/`: Custom exception classes and handlers.
  - `model/`: Data models, DTOs, enums, and POJOs.
  - `repository/`: Data access objects, entities, and repository interfaces.
  - `service/`: Business logic and service classes.
  - `utils/`: Utility/helper classes.
- Place configuration files in `src/main/resources/`.
- Keep build and deployment files (`Dockerfile`, `Jenkinsfile`, `pom.xml`) at the project root.